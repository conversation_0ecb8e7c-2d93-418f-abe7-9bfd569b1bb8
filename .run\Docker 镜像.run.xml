<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Docker 镜像" type="docker-deploy" factoryName="docker-image" server-name="Docker">
    <deployment type="docker-image">
      <settings>
        <option name="imageTag" value="yineng_build:latest" />
        <option name="containerName" value="yineng0_3" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="22" />
              <option name="hostPort" value="2222" />
            </DockerPortBindingImpl>
            <DockerPortBindingImpl>
              <option name="containerPort" value="5173" />
              <option name="hostPort" value="5173" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="showCommandPreview" value="true" />
        <option name="volumeBindings">
          <list>
            <DockerVolumeBindingImpl>
              <option name="containerPath" value="/root/yineng/frontend/src" />
              <option name="hostPath" value="C:\Users\<USER>\Desktop\yineng\frontend\src" />
            </DockerVolumeBindingImpl>
            <DockerVolumeBindingImpl>
              <option name="containerPath" value="/root/yineng/backend" />
              <option name="hostPath" value="C:\Users\<USER>\Desktop\yineng\backend" />
            </DockerVolumeBindingImpl>
          </list>
        </option>
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
  <configuration default="false" name="Docker 镜像" type="docker-deploy" factoryName="docker-image" server-name="Docker">
    <deployment type="docker-image">
      <settings>
        <option name="imageTag" value="yineng_build:latest" />
        <option name="containerName" value="yineng0_3" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="22" />
              <option name="hostPort" value="2222" />
            </DockerPortBindingImpl>
            <DockerPortBindingImpl>
              <option name="containerPort" value="5173" />
              <option name="hostPort" value="5173" />
            </DockerPortBindingImpl>
          </list>
        </option>
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
  <configuration default="false" name="Docker 镜像" type="docker-deploy" factoryName="docker-image" server-name="Docker">
    <deployment type="docker-image">
      <settings>
        <option name="imageTag" value="yineng_build:latest" />
        <option name="containerName" value="yineng0_3" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="22" />
              <option name="hostPort" value="2222" />
            </DockerPortBindingImpl>
            <DockerPortBindingImpl>
              <option name="containerPort" value="5173" />
              <option name="hostPort" value="5173" />
            </DockerPortBindingImpl>
            <DockerPortBindingImpl>
              <option name="containerPort" value="5174" />
              <option name="hostPort" value="5174" />
            </DockerPortBindingImpl>
          </list>
        </option>
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
</component>