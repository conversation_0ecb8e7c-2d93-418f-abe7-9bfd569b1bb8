<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用例管理平台助手</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="popup-container">
        <!-- 头部区域 -->
        <header class="popup-header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🎯</span>
                    <span class="logo-text">用例管理平台</span>
                </div>
                <div class="version">v1.0.0</div>
            </div>
        </header>
        
        <!-- 状态区域 -->
        <section class="status-section">
            <div class="status-item">
                <div class="status-label">连接状态</div>
                <div class="status-value">
                    <span class="status-indicator" id="connection-status"></span>
                    <span id="connection-text">检查中...</span>
                </div>
            </div>
            
            <div class="status-item">
                <div class="status-label">活跃标签页</div>
                <div class="status-value">
                    <span class="status-number" id="active-tabs">0</span>
                    <span class="status-unit">个</span>
                </div>
            </div>
        </section>
        
        <!-- 快速操作区域 -->
        <section class="quick-actions">
            <h3>快速操作</h3>
            <div class="action-grid">
                <button class="action-item" id="toggle-floating-window">
                    <span class="action-icon">👁️</span>
                    <span class="action-text">切换悬浮窗</span>
                </button>
                
                <button class="action-item" id="open-platform">
                    <span class="action-icon">🚀</span>
                    <span class="action-text">打开平台</span>
                </button>
                
                <button class="action-item" id="refresh-connection">
                    <span class="action-icon">🔄</span>
                    <span class="action-text">刷新连接</span>
                </button>
                
                <button class="action-item" id="export-settings">
                    <span class="action-icon">📋</span>
                    <span class="action-text">导出设置</span>
                </button>
            </div>
        </section>
        
        <!-- 设置区域 -->
        <section class="settings-section">
            <h3>设置</h3>
            <div class="settings-list">
                <div class="setting-item">
                    <label for="enable-floating">启用悬浮窗</label>
                    <input type="checkbox" id="enable-floating" checked>
                </div>
                
                <div class="setting-item">
                    <label for="auto-start">自动启动</label>
                    <input type="checkbox" id="auto-start">
                </div>
                
                <div class="setting-item">
                    <label for="transparency">透明度</label>
                    <div class="slider-container">
                        <input type="range" id="transparency" min="0.3" max="1" step="0.1" value="0.9">
                        <span class="slider-value" id="transparency-value">90%</span>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 统计信息 -->
        <section class="stats-section">
            <h3>统计信息</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="total-requests">0</div>
                    <div class="stat-label">总请求数</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number" id="success-rate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number" id="uptime">0h</div>
                    <div class="stat-label">运行时间</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number" id="data-size">0KB</div>
                    <div class="stat-label">数据大小</div>
                </div>
            </div>
        </section>
        
        <!-- 最近活动 -->
        <section class="activity-section">
            <h3>最近活动</h3>
            <div class="activity-list" id="activity-list">
                <div class="activity-item">
                    <div class="activity-time">刚刚</div>
                    <div class="activity-text">扩展启动成功</div>
                </div>
            </div>
        </section>
        
        <!-- 底部链接 -->
        <footer class="popup-footer">
            <div class="footer-links">
                <a href="#" id="help-link">帮助</a>
                <a href="#" id="feedback-link">反馈</a>
                <a href="#" id="about-link">关于</a>
            </div>
            <div class="footer-copyright">
                © 2024 用例管理平台助手
            </div>
        </footer>
    </div>
    
    <!-- 模态框 -->
    <div class="modal" id="help-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>帮助信息</h3>
                <button class="close-modal" id="close-help">×</button>
            </div>
            <div class="modal-body">
                <h4>快捷键</h4>
                <ul>
                    <li><kbd>Alt</kbd> + <kbd>U</kbd> - 切换悬浮窗显示/隐藏</li>
                    <li>右键菜单 - 快速操作</li>
                </ul>
                
                <h4>功能说明</h4>
                <ul>
                    <li>悬浮窗会在每个网页上显示，提供快速访问</li>
                    <li>支持拖拽和调整大小</li>
                    <li>可以通过设置调整透明度</li>
                    <li>数据会在用户之间隔离</li>
                </ul>
                
                <h4>注意事项</h4>
                <ul>
                    <li>首次使用需要刷新页面</li>
                    <li>某些网站可能会阻止悬浮窗显示</li>
                    <li>数据会定期同步到云端</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html> 