/**
 * 认证工具函数
 * 提供统一的token管理和认证相关功能
 */

// Token存储键名
export const TOKEN_KEYS = {
  AUTH_TOKEN: 'auth_token',
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_PROFILE: 'user_profile',
  TOKEN_EXPIRES: 'token_expires',
} as const

/**
 * 获取认证token
 * 按优先级从不同存储位置获取token
 */
export function getAuthToken(): string | null {
  // 1. 优先从localStorage获取
  let token =
    localStorage.getItem(TOKEN_KEYS.AUTH_TOKEN) || localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN)
  if (token) return token

  // 2. 从sessionStorage获取
  token =
    sessionStorage.getItem(TOKEN_KEYS.AUTH_TOKEN) || sessionStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN)
  if (token) return token

  return null
}

/**
 * 设置认证token
 * @param token - JWT token
 * @param persistent - 是否持久化存储（localStorage vs sessionStorage）
 */
export function setAuthToken(token: string, persistent: boolean = false): void {
  const storage = persistent ? localStorage : sessionStorage

  // 清除另一个存储中的token
  const otherStorage = persistent ? sessionStorage : localStorage
  otherStorage.removeItem(TOKEN_KEYS.AUTH_TOKEN)
  otherStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN)

  // 设置新token
  storage.setItem(TOKEN_KEYS.AUTH_TOKEN, token)
  storage.setItem(TOKEN_KEYS.ACCESS_TOKEN, token)
}

/**
 * 清除所有认证信息
 */
export function clearAuthTokens(): void {
  // 清除localStorage
  Object.values(TOKEN_KEYS).forEach((key) => {
    localStorage.removeItem(key)
    sessionStorage.removeItem(key)
  })
}

/**
 * 检查token是否存在
 */
export function hasAuthToken(): boolean {
  return !!getAuthToken()
}

/**
 * 获取token的Authorization头
 */
export function getAuthHeader(): Record<string, string> {
  const token = getAuthToken()
  return token ? { Authorization: `Bearer ${token}` } : {}
}

/**
 * 解析JWT token（不验证签名）
 */
export function parseJwtToken(token: string): any {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join(''),
    )
    return JSON.parse(jsonPayload)
  } catch (_error) {
    return null
  }
}

/**
 * 检查token是否过期
 */
export function isTokenExpired(token?: string): boolean {
  const tokenToCheck = token || getAuthToken()
  if (!tokenToCheck) return true

  try {
    const payload = parseJwtToken(tokenToCheck)
    if (!payload || !payload.exp) return true

    // JWT的exp是秒级时间戳，需要转换为毫秒
    const expirationTime = payload.exp * 1000
    return Date.now() >= expirationTime
  } catch (_error) {
    return true
  }
}

/**
 * 获取token剩余有效时间（分钟）
 */
export function getTokenRemainingTime(token?: string): number {
  const tokenToCheck = token || getAuthToken()
  if (!tokenToCheck) return 0

  try {
    const payload = parseJwtToken(tokenToCheck)
    if (!payload || !payload.exp) return 0

    const expirationTime = payload.exp * 1000
    const remainingTime = expirationTime - Date.now()
    return Math.max(0, Math.floor(remainingTime / (60 * 1000)))
  } catch (_error) {
    return 0
  }
}

/**
 * 检查token是否即将过期
 * @param minutes - 提前多少分钟算作即将过期
 */
export function isTokenExpiringSoon(minutes: number = 5, token?: string): boolean {
  const remainingTime = getTokenRemainingTime(token)
  return remainingTime <= minutes && remainingTime > 0
}

/**
 * 创建带认证头的fetch选项
 */
export function createAuthenticatedFetchOptions(options: RequestInit = {}): RequestInit {
  const authHeader = getAuthHeader()

  return {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...authHeader,
      ...options.headers,
    },
  }
}

/**
 * 带认证的fetch请求
 */
export async function authenticatedFetch(
  url: string,
  options: RequestInit = {},
): Promise<Response> {
  const authenticatedOptions = createAuthenticatedFetchOptions(options)

  const response = await fetch(url, authenticatedOptions)

  // 处理401错误
  if (response.status === 401) {
    clearAuthTokens()

    // 可以在这里触发全局认证错误处理
    window.dispatchEvent(
      new CustomEvent('auth-error', {
        detail: { message: '认证失败，请重新登录' },
      }),
    )
  }

  return response
}

/**
 * 监听认证错误事件
 */
export function onAuthError(callback: (event: CustomEvent) => void): () => void {
  window.addEventListener('auth-error', callback as EventListener)

  // 返回清理函数
  return () => {
    window.removeEventListener('auth-error', callback as EventListener)
  }
}

/**
 * 验证当前token有效性
 */
export async function validateCurrentToken(): Promise<boolean> {
  const token = getAuthToken()
  if (!token) return false

  // 首先检查token格式和过期时间
  if (isTokenExpired(token)) {
    clearAuthTokens()
    return false
  }

  // 可以在这里添加服务器端验证逻辑
  // 例如调用 /api/v1/auth/validate 接口

  return true
}
