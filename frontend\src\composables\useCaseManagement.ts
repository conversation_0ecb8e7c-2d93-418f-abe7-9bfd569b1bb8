import { reactive, computed, readonly } from 'vue'
import { ElMessage } from 'element-plus'
import { apiService } from '@/services/api'
import { transformSystemsData, separateLibraryData, findNodeById, getNodePath } from '@/utils/caseDataTransform'
import type {
  CaseNode,
  TransformedSystemsData,
  CaseLibraryData,
  UpdateCaseStatusRequest,
  BatchUpdateCaseStatusRequest
} from '@/services/api'

// 用例管理状态
export interface CaseManagementState {
  systems: CaseNode[]
  totalCases: number
  totalSystems: number
  productLibraryCases: number
  candidateLibraryCases: number
  libraryData: CaseLibraryData | null
  selectedNodes: Set<string>
  expandedNodes: Set<string>
  searchKeyword: string
  loading: boolean
  error: string | null
}

/**
 * 用例管理组合式函数
 */
export function useCaseManagement() {
  // 状态管理
  const state = reactive<CaseManagementState>({
    systems: [],
    totalCases: 0,
    totalSystems: 0,
    productLibraryCases: 0,
    candidateLibraryCases: 0,
    libraryData: null,
    selectedNodes: new Set(),
    expandedNodes: new Set(),
    searchKeyword: '',
    loading: false,
    error: null
  })

  // 计算属性
  const isLoading = computed(() => state.loading)
  const hasError = computed(() => !!state.error)
  const isEmpty = computed(() => state.systems.length === 0)
  const selectedCount = computed(() => state.selectedNodes.size)

  // 产品库数据
  const productLibrary = computed(() => state.libraryData?.productLibrary || [])

  // 备选库数据
  const candidateLibrary = computed(() => state.libraryData?.candidateLibrary || [])

  // 统计信息
  const statistics = computed(() => ({
    totalSystems: state.totalSystems,
    totalCases: state.totalCases,
    productLibraryCases: state.productLibraryCases,
    candidateLibraryCases: state.candidateLibraryCases,
    selectedCount: selectedCount.value
  }))

  // 过滤后的系统数据
  const filteredSystems = computed(() => {
    if (!state.searchKeyword.trim()) {
      return state.systems
    }

    const keyword = state.searchKeyword.toLowerCase()

    const filterNodes = (nodes: CaseNode[]): CaseNode[] => {
      return nodes.reduce((filtered: CaseNode[], node) => {
        const matchesKeyword = node.displayName.toLowerCase().includes(keyword) ||
                              node.name.toLowerCase().includes(keyword)

        let filteredChildren: CaseNode[] = []
        if (node.children) {
          filteredChildren = filterNodes(node.children)
        }

        // 如果节点本身匹配或有匹配的子节点，则包含此节点
        if (matchesKeyword || filteredChildren.length > 0) {
          filtered.push({
            ...node,
            children: filteredChildren.length > 0 ? filteredChildren : node.children
          })
        }

        return filtered
      }, [])
    }

    return filterNodes(state.systems)
  })

  /**
   * 获取系统目录数据
   */
  const fetchSystems = async (): Promise<boolean> => {
    try {
      state.loading = true
      state.error = null

      console.log('🚀 开始获取系统数据...')
      console.log('📡 API基础URL:', import.meta.env.VITE_API_BASE_URL)
      console.log('🔗 请求URL:', '/api/v1/case-management/systems')

      // 直接从API获取数据
      const systemsData = await apiService.caseManagement.getSystems()
      console.log('✅ API请求成功')

      console.log('🌐 API响应（系统数据）:', systemsData)
      console.log('🔍 API响应类型检查:', {
        type: typeof systemsData,
        isArray: Array.isArray(systemsData),
        isNull: systemsData === null,
        isUndefined: systemsData === undefined,
        keys: systemsData && typeof systemsData === 'object' ? Object.keys(systemsData) : 'N/A',
        stringified: JSON.stringify(systemsData, null, 2)
      })
      console.log('📊 系统数据结构:', {
        dataType: typeof systemsData,
        dataKeys: systemsData ? Object.keys(systemsData) : [],
        dataContent: systemsData
      })

      // 检查系统数据的有效性
      const hasValidData = systemsData && typeof systemsData === 'object' && !Array.isArray(systemsData)

      console.log('🔍 数据有效性检查:', {
        hasValidData,
        dataType: typeof systemsData,
        dataKeys: systemsData ? Object.keys(systemsData) : [],
        isObject: typeof systemsData === 'object',
        isArray: Array.isArray(systemsData)
      })

      if (hasValidData) {
        console.log('✅ 开始数据转换...')
        const transformedData: TransformedSystemsData = transformSystemsData(systemsData as any)
        const libraryData = separateLibraryData(systemsData as any)

        console.log('🔄 转换结果:', {
          transformedData,
          libraryData
        })

        state.systems = transformedData.systems
        state.totalCases = transformedData.totalCases
        state.totalSystems = transformedData.totalSystems
        state.productLibraryCases = transformedData.productLibraryCases
        state.candidateLibraryCases = transformedData.candidateLibraryCases
        state.libraryData = libraryData

        console.log('✅ 状态更新完成:')
        console.log('  - systems:', state.systems.length, '个系统')
        console.log('  - libraryData:', state.libraryData)
        console.log('  - productLibrary:', state.libraryData?.productLibrary?.length || 0, '个系统')
        console.log('  - candidateLibrary:', state.libraryData?.candidateLibrary?.length || 0, '个系统')

        // 默认展开第一级节点
        state.expandedNodes.clear()
        state.systems.forEach(system => {
          state.expandedNodes.add(system.id)
        })

        return true
      } else {
        const errorMsg = '获取系统目录失败：数据格式无效'
        state.error = errorMsg
        console.error('❌ 数据转换失败:', {
          systemsData,
          errorMsg,
          hasValidData,
          dataType: typeof systemsData
        })
        ElMessage.error(state.error)
        return false
      }
    } catch (error: any) {
      console.error('❌ 获取系统数据失败:', error)
      console.error('🔍 错误详情:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL
        }
      })

      let errorMessage = '获取系统数据失败'
      if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
        errorMessage = '网络连接失败，请检查后端服务是否启动'
      } else if (error.response?.status === 404) {
        errorMessage = 'API接口不存在，请检查后端路由配置'
      } else if (error.response?.status === 401) {
        errorMessage = '认证失败，请重新登录'
      } else if (error.response?.status >= 500) {
        errorMessage = '服务器内部错误，请联系管理员'
      } else if (error.message) {
        errorMessage = error.message
      }

      state.error = errorMessage
      ElMessage.error(errorMessage)
      return false
    } finally {
      state.loading = false
    }
  }

  /**
   * 刷新系统目录
   */
  const refreshSystems = async (): Promise<boolean> => {
    ElMessage.info('正在刷新系统目录...')
    return await fetchSystems()
  }

  /**
   * 搜索用例
   */
  const searchCases = (keyword: string): void => {
    state.searchKeyword = keyword

    // 如果有搜索关键词，展开所有匹配的节点
    if (keyword.trim()) {
      const expandMatchingNodes = (nodes: CaseNode[]): void => {
        nodes.forEach(node => {
          const matches = node.displayName.toLowerCase().includes(keyword.toLowerCase()) ||
                         node.name.toLowerCase().includes(keyword.toLowerCase())

          if (matches || (node.children && hasMatchingChildren(node.children, keyword))) {
            state.expandedNodes.add(node.id)
          }

          if (node.children) {
            expandMatchingNodes(node.children)
          }
        })
      }

      expandMatchingNodes(state.systems)
    }
  }

  /**
   * 检查是否有匹配的子节点
   */
  const hasMatchingChildren = (nodes: CaseNode[], keyword: string): boolean => {
    return nodes.some(node => {
      const matches = node.displayName.toLowerCase().includes(keyword.toLowerCase()) ||
                     node.name.toLowerCase().includes(keyword.toLowerCase())

      return matches || (node.children && hasMatchingChildren(node.children, keyword))
    })
  }

  /**
   * 清空搜索
   */
  const clearSearch = (): void => {
    state.searchKeyword = ''
  }

  /**
   * 选择/取消选择节点
   */
  const toggleNodeSelection = (nodeId: string): void => {
    if (state.selectedNodes.has(nodeId)) {
      state.selectedNodes.delete(nodeId)
    } else {
      state.selectedNodes.add(nodeId)
    }
  }

  /**
   * 选择多个节点
   */
  const selectNodes = (nodeIds: string[]): void => {
    nodeIds.forEach(id => state.selectedNodes.add(id))
  }

  /**
   * 取消选择多个节点
   */
  const deselectNodes = (nodeIds: string[]): void => {
    nodeIds.forEach(id => state.selectedNodes.delete(id))
  }

  /**
   * 清空所有选择
   */
  const clearSelection = (): void => {
    state.selectedNodes.clear()
  }

  /**
   * 展开/折叠节点
   */
  const toggleNodeExpansion = (nodeId: string): void => {
    if (state.expandedNodes.has(nodeId)) {
      state.expandedNodes.delete(nodeId)
    } else {
      state.expandedNodes.add(nodeId)
    }
  }

  /**
   * 展开所有节点
   */
  const expandAll = (): void => {
    const expandNodes = (nodes: CaseNode[]): void => {
      nodes.forEach(node => {
        state.expandedNodes.add(node.id)
        if (node.children) {
          expandNodes(node.children)
        }
      })
    }

    expandNodes(state.systems)
  }

  /**
   * 折叠所有节点
   */
  const collapseAll = (): void => {
    state.expandedNodes.clear()
  }

  /**
   * 根据ID查找节点
   */
  const findNode = (nodeId: string): CaseNode | null => {
    return findNodeById(state.systems, nodeId)
  }

  /**
   * 获取节点路径
   */
  const getPath = (nodeId: string): CaseNode[] => {
    return getNodePath(state.systems, nodeId)
  }

  /**
   * 获取选中的节点
   */
  const getSelectedNodes = (): CaseNode[] => {
    const selectedNodes: CaseNode[] = []

    state.selectedNodes.forEach(nodeId => {
      const node = findNode(nodeId)
      if (node) {
        selectedNodes.push(node)
      }
    })

    return selectedNodes
  }

  /**
   * 重置状态
   */
  const resetState = (): void => {
    state.systems = []
    state.totalCases = 0
    state.totalSystems = 0
    state.selectedNodes.clear()
    state.expandedNodes.clear()
    state.searchKeyword = ''
    state.loading = false
    state.error = null
  }

  /**
   * 获取统计信息
   */
  const getStatistics = () => {
    return {
      totalSystems: state.totalSystems,
      totalCases: state.totalCases,
      productLibraryCases: state.productLibraryCases,
      candidateLibraryCases: state.candidateLibraryCases,
      selectedCount: selectedCount.value,
      filteredCount: filteredSystems.value.length
    }
  }

  /**
   * 更新用例状态
   */
  const updateCaseStatus = async (updateData: UpdateCaseStatusRequest): Promise<boolean> => {
    try {
      const response = await apiService.caseManagement.updateCaseStatus(updateData)

      if (response.success) {
        // 刷新数据
        await fetchSystems()
        ElMessage.success('用例状态更新成功')
        return true
      } else {
        throw new Error(response.message || '更新用例状态失败')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '更新用例状态失败')
      console.error('更新用例状态失败:', error)
      return false
    }
  }

  /**
   * 批量更新用例状态
   */
  const batchUpdateCaseStatus = async (updates: UpdateCaseStatusRequest[]): Promise<boolean> => {
    try {
      const batchData: BatchUpdateCaseStatusRequest = { updates }
      const response = await apiService.caseManagement.batchUpdateCaseStatus(batchData.updates)

      if (response.success) {
        // 刷新数据
        await fetchSystems()
        ElMessage.success(`批量更新成功：${(response as any).data?.successCount || batchData.updates.length}个用例`)
        return true
      } else {
        throw new Error(response.message || '批量更新用例状态失败')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '批量更新用例状态失败')
      console.error('批量更新用例状态失败:', error)
      return false
    }
  }

  /**
   * 移动用例到指定库
   */
  const moveCaseToLibrary = async (
    caseId: string,
    fromLibrary: 'product' | 'candidate',
    toLibrary: 'product' | 'candidate'
  ): Promise<boolean> => {
    try {
      const response = await apiService.caseManagement.moveCaseBetweenLibraries({
        caseId,
        fromLibrary,
        toLibrary
      })

      if (response.success) {
        // 刷新数据
        await fetchSystems()
        const libraryName = toLibrary === 'product' ? '产品库' : '备选库'
        ElMessage.success(`用例已移动到${libraryName}`)
        return true
      } else {
        throw new Error(response.message || '移动用例失败')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '移动用例失败')
      console.error('移动用例失败:', error)
      return false
    }
  }

  return {
    // 状态
    state: readonly(state),

    // 计算属性
    isLoading,
    hasError,
    isEmpty,
    selectedCount,
    filteredSystems,
    productLibrary,
    candidateLibrary,
    statistics,

    // 方法
    fetchSystems,
    refreshSystems,
    searchCases,
    clearSearch,
    toggleNodeSelection,
    selectNodes,
    deselectNodes,
    clearSelection,
    toggleNodeExpansion,
    expandAll,
    collapseAll,
    findNode,
    getPath,
    getSelectedNodes,
    resetState,
    getStatistics,
    updateCaseStatus,
    batchUpdateCaseStatus,
    moveCaseToLibrary
  }
}

// 注意：不要创建全局实例，应该在组件中按需使用
// export const globalCaseManagement = useCaseManagement()
