<template>
  <div class="user-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="handleAddUser" v-permission="Permission.SYSTEM_ADMIN">
            添加用户
          </el-button>
        </div>
      </template>

      <el-table
        :data="users"
        style="width: 100%"
        v-loading="loading"
        element-loading-text="加载用户列表中..."
      >
        <el-table-column prop="_id" label="用户ID" width="120">
          <template #default="scope">
            <el-tooltip :content="scope.row._id" placement="top">
              <span>{{ scope.row._id.slice(-8) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag :type="getRoleTagType(scope.row.role)">
              {{ getRoleLabel(scope.row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
              {{ scope.row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.last_login) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              size="small"
              type="danger"
              @click="handleDeleteUser(scope.row)"
              v-permission:disable="Permission.SYSTEM_ADMIN"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; text-align: right"
      />
    </el-card>

    <!-- 用户创建对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="添加用户"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="userForm.username"
            placeholder="请输入用户名（3-20个字符，仅支持字母、数字、下划线）"
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" style="width: 100%" placeholder="请选择角色">
            <el-option
              v-for="role in availableRoles"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            show-password
            placeholder="请输入密码（至少6个字符）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false" :disabled="saving">取消</el-button>
        <el-button
          type="primary"
          @click="handleSaveUser"
          :loading="saving"
          :disabled="saving"
        >
          {{ saving ? '创建中...' : '创建用户' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usePermission } from '@/composables/usePermission'
import { Permission } from '@/types/api/user'
import { apiService } from '@/services/api'
import type {
  UserManagementProfile,
  CreateUserRequest,
  UserListResponse
} from '@/types/api/user'

// 权限检查
const { hasPermission } = usePermission()

// 用户列表状态
const users = ref<UserManagementProfile[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 编辑用户对话框
const editDialogVisible = ref(false)
const editingUser = ref<UserManagementProfile | null>(null)
const userFormRef = ref()
const saving = ref(false)

// 用户表单类型（支持创建和编辑）
interface UserFormData extends CreateUserRequest {
  assigned_systems?: string[]
}

// 用户表单
const userForm = ref<UserFormData>({
  username: '',
  password: '',
  role: 'user',
  is_active: true,
  permissions: undefined,
  assigned_systems: []
})

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 7, message: '密码长度不能少于7个字符', trigger: 'blur' },
  ],
}

// 可用角色
const availableRoles = [
  { label: '管理员', value: 'admin' },
  { label: '用户', value: 'user' },
]

// 可用系统
const availableSystems = [
  { label: '用例管理系统', value: 'usecase_management' },
  { label: '测试执行系统', value: 'test_execution' },
  { label: '报告生成系统', value: 'report_generation' },
]

// 获取角色标签类型
const getRoleTagType = (role: string) => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'user':
      return 'primary'
    default:
      return 'info'
  }
}

// 获取角色标签
const getRoleLabel = (role: string) => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'user':
      return '用户'
    default:
      return '未知'
  }
}

// 获取用户列表
const fetchUsers = async () => {
  if (!hasPermission(Permission.SYSTEM_ADMIN)) {
    ElMessage.error('您没有权限查看用户列表')
    return
  }

  try {
    loading.value = true

    const response = await apiService.userManagement.getUsers()

    if (response.success && response.data) {
      users.value = response.data.users || []
      total.value = response.data.total || 0
      ElMessage.success(`用户列表加载成功，共 ${total.value} 个用户`)
    } else {
      throw new Error(response.message || '获取用户列表失败')
    }
  } catch (error: any) {
    ElMessage.error(`加载用户列表失败: ${error.message || '未知错误'}`)
    users.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理添加用户
const handleAddUser = () => {
  if (!hasPermission(Permission.SYSTEM_ADMIN)) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  editingUser.value = null
  userForm.value = {
    username: '',
    password: '',
    role: 'user',
    is_active: true,
    permissions: undefined,
    assigned_systems: []
  }
  editDialogVisible.value = true
}

// 处理编辑用户
const handleEdit = (user: UserManagementProfile) => {
  if (!hasPermission(Permission.SYSTEM_ADMIN)) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  editingUser.value = user
  userForm.value = {
    username: user.username,
    role: user.role,
    is_active: user.is_active,
    permissions: user.permissions,
    assigned_systems: (user as any).assigned_systems || [],
    password: '',
  }
  editDialogVisible.value = true
}

// 处理重置密码
const handleResetPassword = async (user: UserManagementProfile) => {
  if (!hasPermission(Permission.SYSTEM_ADMIN)) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要重置用户 "${user.username}" 的密码吗？`, '重置密码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 模拟API调用
    ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
  } catch {
    // 用户取消
  }
}

// 处理切换用户状态
const handleToggleStatus = async (user: UserManagementProfile) => {
  if (!hasPermission(Permission.SYSTEM_ADMIN)) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要${user.is_active ? '禁用' : '激活'}用户 "${user.username}" 吗？`,
      '切换用户状态',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 模拟API调用
    user.is_active = !user.is_active
    ElMessage.success(`用户${user.is_active ? '激活' : '禁用'}成功`)
  } catch {
    // 用户取消
  }
}

// 处理保存用户
const handleSaveUser = async () => {
  if (!userFormRef.value) {
    ElMessage.error('表单引用无效')
    return
  }

  if (!hasPermission(Permission.SYSTEM_ADMIN)) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  try {
    await userFormRef.value.validate()
    saving.value = true

    console.log('创建用户请求数据:', userForm.value)

    // 构造符合API要求的请求数据
    const createUserData: CreateUserRequest = {
      username: userForm.value.username,
      password: userForm.value.password,
      role: userForm.value.role,
      is_active: userForm.value.is_active,
      permissions: userForm.value.permissions
    }

    const response = await apiService.userManagement.createUser(createUserData)

    console.log('创建用户API响应:', response)

    if (response.success) {
      ElMessage.success('用户创建成功')
      editDialogVisible.value = false
      await fetchUsers()
    } else {
      throw new Error(response.message || '创建用户失败')
    }
  } catch (error: any) {
    console.error('创建用户失败:', error)
    ElMessage.error(`创建用户失败: ${error.message || '未知错误'}`)
  } finally {
    saving.value = false
  }
}

// 处理删除用户
const handleDeleteUser = async (user: UserManagementProfile) => {
  if (!hasPermission(Permission.SYSTEM_ADMIN)) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复。\n被删除用户的所有活跃会话将被强制退出。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 1. 调用API删除用户
    const response = await apiService.userManagement.deleteUser(user._id)

    if (response.success) {
      // 2. 导入并使用会话管理器清理被删除用户的会话
      const { userSessionManager } = await import('@/services/userSessionManager')
      await userSessionManager.handleUserDeleted(user.username)

      ElMessage.success(`用户 ${user.username} 删除成功，相关会话已清理`)
      await fetchUsers()
    } else {
      throw new Error(response.message || '删除用户失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error(`删除用户失败: ${error.message || '未知错误'}`)
    }
  }
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  fetchUsers()
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchUsers()
}

// 格式化时间显示
const formatDateTime = (dateString: string | null) => {
  if (!dateString) return '从未登录'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return '无效时间'
  }
}

// 初始化
onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
