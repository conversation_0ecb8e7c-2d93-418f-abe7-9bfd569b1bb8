import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    host: '0.0.0.0', // 绑定到所有网络接口，允许外部访问
    port: 5173, // 明确指定端口
    strictPort: true, // 如果端口被占用则失败，而不是尝试下一个端口
    proxy: {
      // 将 /api 请求代理到后端服务
      '/api': {
        target: 'http://*************:8000', // 后端服务器地址
        changeOrigin: true, // 必须设置为 true
      },
    },
  },
})
