<template>
  <div class="tech-input-wrapper" :class="{ focused: isFocused }">
    <label v-if="label" :for="inputId" class="input-label">{{ label }}</label>
    <div class="input-container">
      <div v-if="prefixIcon" class="icon-prefix">
        <el-icon><component :is="prefixIcon" /></el-icon>
      </div>
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        class="tech-input"
        @input="onInput"
        @focus="onFocus"
        @blur="onBlur"
      />
      <div class="focus-line"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElIcon } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  label: String,
  type: {
    type: String,
    default: 'text',
  },
  placeholder: String,
  prefixIcon: [String, Object],
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur'])

const isFocused = ref(false)
const inputId = computed(() => `tech-input-${Math.random().toString(36).substr(2, 9)}`)

const onInput = (event: Event) => {
  emit('update:modelValue', (event.target as HTMLInputElement).value)
}

const onFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('focus', event)
}

const onBlur = (event: FocusEvent) => {
  isFocused.value = false
  emit('blur', event)
}
</script>

<style scoped lang="scss">
@use '@/assets/styles/theme.scss' as theme;

.tech-input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.input-label {
  font-family: theme.$font-family-tech;
  color: theme.$color-text-secondary;
  font-size: 0.9rem;
  margin-bottom: 8px;
  transition: color theme.$transition-fast;
}

.focused .input-label {
  color: theme.$color-primary-accent;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgba(theme.$color-tertiary-accent, 0.3);
  border-radius: theme.$border-radius-tech;
  transition: background-color theme.$transition-fast;
}

.focused .input-container {
  background-color: rgba(theme.$color-tertiary-accent, 0.5);
}

.icon-prefix {
  padding: 0 12px;
  color: theme.$color-text-secondary;
  transition: color theme.$transition-fast;
}

.focused .icon-prefix {
  color: theme.$color-text-primary;
}

.tech-input {
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
  padding: 12px;
  color: theme.$color-text-primary;
  font-family: theme.$font-family-sans;
  font-size: 1rem;

  &::placeholder {
    color: theme.$color-text-secondary;
  }
}

.focus-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: theme.$color-primary-accent;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform theme.$transition-normal;
}

.focused .focus-line {
  transform: scaleX(1);
}
</style>
