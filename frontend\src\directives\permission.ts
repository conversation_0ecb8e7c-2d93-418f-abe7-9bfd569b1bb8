import type { App, DirectiveBinding } from 'vue'
import { usePermission } from '@/composables/usePermission'
import { Permission } from '@/types/api/user'

// 权限指令接口
interface PermissionDirectiveBinding extends Omit<DirectiveBinding, 'modifiers'> {
  value: Permission | Permission[] | string | string[]
  arg?: 'hide' | 'disable'
  modifiers?: {
    hide?: boolean
    disable?: boolean
  }
}

// 权限指令实现
const permissionDirective = {
  mounted(el: HTMLElement, binding: PermissionDirectiveBinding) {
    checkPermission(el, binding)
  },
  updated(el: HTMLElement, binding: PermissionDirectiveBinding) {
    checkPermission(el, binding)
  },
}

// 检查权限
function checkPermission(el: HTMLElement, binding: PermissionDirectiveBinding) {
  const { hasPermission, hasAnyPermission } = usePermission()
  const { value, arg, modifiers } = binding

  if (!value) {
    return
  }

  let permissions: Permission[] = []

  // 处理不同类型的权限值
  if (typeof value === 'string') {
    permissions = [value as Permission]
  } else if (Array.isArray(value)) {
    permissions = value as Permission[]
  } else {
    return
  }

  // 检查权限
  const hasAccess =
    permissions.length === 1 ? hasPermission(permissions[0]) : hasAnyPermission(permissions)

  // 确定操作类型
  const shouldHide = arg === 'hide' || modifiers?.hide
  const shouldDisable = arg === 'disable' || modifiers?.disable

  if (!hasAccess) {
    if (shouldHide) {
      // 隐藏元素
      el.style.display = 'none'
      el.classList.add('permission-hidden')
    } else if (shouldDisable) {
      // 禁用元素
      el.style.opacity = '0.6'
      el.style.cursor = 'not-allowed'
      el.classList.add('permission-disabled')

      // 禁用表单元素
      if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' || el.tagName === 'SELECT') {
        ;(el as HTMLInputElement).disabled = true
      }

      // 禁用按钮
      if (el.tagName === 'BUTTON') {
        ;(el as HTMLButtonElement).disabled = true
      }

      // 阻止点击事件
      el.addEventListener('click', preventDefault, true)
    } else {
      // 默认隐藏
      el.style.display = 'none'
      el.classList.add('permission-hidden')
    }
  } else {
    // 有权限，恢复元素状态
    if (shouldHide) {
      el.style.display = ''
      el.classList.remove('permission-hidden')
    } else if (shouldDisable) {
      el.style.opacity = ''
      el.style.cursor = ''
      el.classList.remove('permission-disabled')

      // 恢复表单元素
      if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' || el.tagName === 'SELECT') {
        ;(el as HTMLInputElement).disabled = false
      }

      // 恢复按钮
      if (el.tagName === 'BUTTON') {
        ;(el as HTMLButtonElement).disabled = false
      }

      // 移除点击事件阻止
      el.removeEventListener('click', preventDefault, true)
    } else {
      el.style.display = ''
      el.classList.remove('permission-hidden')
    }
  }
}

// 阻止默认事件
function preventDefault(event: Event) {
  event.preventDefault()
  event.stopPropagation()
}

// 权限指令插件
export default {
  install(app: App) {
    app.directive('permission', permissionDirective)
  },
}

// 导出指令
export { permissionDirective }
