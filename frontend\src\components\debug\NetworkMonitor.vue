<template>
  <div class="network-monitor">
    <div class="monitor-header">
      <h4>🌐 悬浮窗网络请求监控</h4>
      <div class="monitor-controls">
        <el-button size="small" @click="clearRequests">清空</el-button>
        <el-button size="small" @click="toggleAutoScroll">
          {{ autoScroll ? '停止滚动' : '自动滚动' }}
        </el-button>
        <el-button size="small" @click="exportRequests">导出日志</el-button>
      </div>
    </div>
    
    <div class="monitor-content" ref="contentRef">
      <div v-if="requests.length === 0" class="empty-state">
        <p>暂无网络请求记录</p>
        <p class="hint">在悬浮窗中进行录制操作时，相关的API请求将显示在这里</p>
      </div>
      
      <div v-for="request in requests" :key="request.id" class="request-item">
        <div class="request-header" @click="toggleRequest(request.id)">
          <div class="request-info">
            <span class="method" :class="request.method.toLowerCase()">{{ request.method }}</span>
            <span class="url">{{ request.url }}</span>
            <span class="time">{{ formatTime(request.timestamp) }}</span>
          </div>
          <div class="request-status">
            <span v-if="request.status" class="status" :class="getStatusClass(request.status)">
              {{ request.status }}
            </span>
            <span v-if="request.error" class="error">❌ 错误</span>
            <span v-if="!request.status && !request.error" class="pending">⏳ 进行中</span>
          </div>
        </div>
        
        <div v-if="expandedRequests.has(request.id)" class="request-details">
          <div class="detail-section">
            <h5>请求信息</h5>
            <div class="detail-content">
              <p><strong>URL:</strong> {{ request.url }}</p>
              <p><strong>方法:</strong> {{ request.method }}</p>
              <p><strong>时间:</strong> {{ new Date(request.timestamp).toLocaleString() }}</p>
              <div v-if="request.headers">
                <strong>请求头:</strong>
                <pre class="json-content">{{ JSON.stringify(request.headers, null, 2) }}</pre>
              </div>
              <div v-if="request.data">
                <strong>请求数据:</strong>
                <pre class="json-content">{{ JSON.stringify(request.data, null, 2) }}</pre>
              </div>
            </div>
          </div>
          
          <div v-if="request.response" class="detail-section">
            <h5>响应信息</h5>
            <div class="detail-content">
              <p><strong>状态码:</strong> {{ request.status }}</p>
              <p><strong>状态文本:</strong> {{ request.statusText }}</p>
              <div v-if="request.response">
                <strong>响应数据:</strong>
                <pre class="json-content">{{ JSON.stringify(request.response, null, 2) }}</pre>
              </div>
            </div>
          </div>
          
          <div v-if="request.error" class="detail-section error-section">
            <h5>错误信息</h5>
            <div class="detail-content">
              <p class="error-message">{{ request.error }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, onUnmounted } from 'vue'
import { ElButton, ElMessage } from 'element-plus'

interface NetworkRequest {
  id: string
  timestamp: number
  method: string
  url: string
  headers?: any
  data?: any
  status?: number
  statusText?: string
  response?: any
  error?: string
}

const requests = ref<NetworkRequest[]>([])
const expandedRequests = reactive(new Set<string>())
const autoScroll = ref(true)
const contentRef = ref<HTMLElement>()

let requestCounter = 0

// 生成请求ID
const generateRequestId = () => `req_${Date.now()}_${++requestCounter}`

// 添加网络请求
const addNetworkRequest = (data: any) => {
  if (data.type === 'request') {
    const request: NetworkRequest = {
      id: generateRequestId(),
      timestamp: data.timestamp,
      method: data.method,
      url: data.url,
      headers: data.headers,
      data: data.data
    }
    requests.value.push(request)
    
    if (autoScroll.value) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = contentRef.value.scrollHeight
        }
      })
    }
  } else if (data.type === 'response') {
    // 找到对应的请求并更新响应信息
    const request = requests.value.find(r => r.url === data.url && !r.status)
    if (request) {
      request.status = data.status
      request.statusText = data.statusText
      request.response = data.data
    }
  } else if (data.type === 'error') {
    // 找到对应的请求并更新错误信息
    const request = requests.value.find(r => r.url === data.url && !r.status && !r.error)
    if (request) {
      request.error = data.error
    }
  }
}

// 切换请求详情展开状态
const toggleRequest = (id: string) => {
  if (expandedRequests.has(id)) {
    expandedRequests.delete(id)
  } else {
    expandedRequests.add(id)
  }
}

// 清空请求记录
const clearRequests = () => {
  requests.value = []
  expandedRequests.clear()
  ElMessage.success('已清空网络请求记录')
}

// 切换自动滚动
const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value
  ElMessage.info(autoScroll.value ? '已开启自动滚动' : '已关闭自动滚动')
}

// 导出请求日志
const exportRequests = () => {
  const data = JSON.stringify(requests.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `floating-window-requests-${new Date().toISOString().slice(0, 19)}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('网络请求日志已导出')
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 获取状态码样式类
const getStatusClass = (status: number) => {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 400 && status < 500) return 'client-error'
  if (status >= 500) return 'server-error'
  return 'info'
}

// 暴露添加请求的方法
defineExpose({
  addNetworkRequest
})
</script>

<style scoped>
.network-monitor {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  margin-top: 20px;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.monitor-header h4 {
  margin: 0;
  color: #333;
  font-size: 14px;
}

.monitor-controls {
  display: flex;
  gap: 8px;
}

.monitor-content {
  max-height: 400px;
  overflow-y: auto;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.empty-state .hint {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.request-item {
  border-bottom: 1px solid #f0f0f0;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.request-header:hover {
  background: #f8f9fa;
}

.request-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.method {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  color: white;
  min-width: 45px;
  text-align: center;
}

.method.get { background: #10b981; }
.method.post { background: #3b82f6; }
.method.put { background: #f59e0b; }
.method.delete { background: #ef4444; }

.url {
  font-family: monospace;
  font-size: 12px;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time {
  font-size: 11px;
  color: #666;
}

.request-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  color: white;
}

.status.success { background: #10b981; }
.status.client-error { background: #f59e0b; }
.status.server-error { background: #ef4444; }
.status.info { background: #6b7280; }

.error {
  color: #ef4444;
  font-size: 12px;
}

.pending {
  color: #f59e0b;
  font-size: 12px;
}

.request-details {
  padding: 16px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 13px;
}

.detail-content p {
  margin: 4px 0;
  font-size: 12px;
  color: #555;
}

.json-content {
  background: #1a1a1a;
  color: #00ff00;
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
  overflow-x: auto;
  margin: 8px 0;
}

.error-section {
  border-left: 4px solid #ef4444;
  padding-left: 12px;
}

.error-message {
  color: #ef4444;
  font-weight: bold;
}
</style>
