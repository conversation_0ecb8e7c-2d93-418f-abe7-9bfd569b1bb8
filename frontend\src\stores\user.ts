import { defineStore } from 'pinia'
import { computed } from 'vue'
import { useMultiUserSystem } from '@/composables/useMultiUserSystem'
import type { UserProfile, LoginForm } from '@/types/api/user'

// 用户信息接口
export interface UserInfo {
  id: string
  username: string
  avatar?: string
  roles: string[]
  permissions: string[]
}

export const useUserStore = defineStore('user', () => {
  // 获取多用户系统
  const multiUserSystem = useMultiUserSystem()

  // 计算属性：当前用户配置文件
  const profile = computed(() => {
    return multiUserSystem.currentUser.value?.profile || null
  })

  // 计算属性：当前用户token
  const token = computed(() => {
    return multiUserSystem.currentUser.value?.token || null
  })

  // 计算属性：是否已登录
  const isLoggedIn = computed(() => {
    return !!multiUserSystem.currentUser.value?.isOnline
  })

  // 计算属性：当前用户ID
  const currentUserId = computed(() => {
    return multiUserSystem.currentUser.value?.id || null
  })

  // 计算属性：当前用户名
  const currentUsername = computed(() => {
    return multiUserSystem.currentUser.value?.username || null
  })

  // 计算属性：所有在线用户
  const allOnlineUsers = computed(() => {
    return multiUserSystem.onlineUsers.value
  })

  // 计算属性：是否可以添加新用户
  const canAddUser = computed(() => {
    return multiUserSystem.canAddUser.value
  })

  // 计算属性：用户信息（兼容旧接口）
  const userInfo = computed(() => {
    return profile.value
  })

  /**
   * 用户登录
   * @param loginForm - 登录表单数据
   */
  async function login(loginForm: LoginForm): Promise<{ success: boolean; message?: string }> {
    try {
      // 使用多用户系统登录
      const result = await multiUserSystem.login(loginForm.username, loginForm.password)

      return result
    } catch (error: any) {
      return { success: false, message: error.message || '登录时发生未知错误' }
    }
  }

  /**
   * 用户登出
   */
  async function logout(): Promise<{ success: boolean; message?: string }> {
    try {
      if (!currentUserId.value) {
        console.warn('没有活跃的账号')
        return { success: false, message: '没有活跃的账号' }
      }

      // 使用多用户系统登出
      const result = await multiUserSystem.logout()

      if (!result.success) {
        console.error('登出失败:', result.message)
      }

      return result
    } catch (error: any) {
      console.error('登出操作失败:', error)
      return { success: false, message: error.message || '登出时发生错误' }
    }
  }

  /**
   * 切换用户
   * @param userId - 要切换到的用户ID
   */
  function switchUser(userId: string): boolean {
    const result = multiUserSystem.switchUser(userId)
    if (!result) {
      console.error('用户切换失败:', userId)
    }
    return result
  }

  /**
   * 获取当前用户的API服务
   */
  function getCurrentApiService() {
    return multiUserSystem.getUserApiService()
  }

  /**
   * 获取当前用户的数据库服务
   */
  function getCurrentDBService() {
    return multiUserSystem.getUserDBService()
  }

  /**
   * 获取当前用户的影子DOM
   */
  function getCurrentShadowRoot() {
    // 删除未使用的getUserShadowRoot方法调用
    return null
  }

  /**
   * 获取系统状态
   */
  function getSystemStatus() {
    return multiUserSystem.getSystemStatus()
  }

  /**
   * 清理所有用户
   */
  async function clearAllUsers(): Promise<void> {
    try {
      // 删除未使用的clearAllUsers方法调用
      // 清理功能已在logout中处理
    } catch (error) {
      console.error('清理所有用户失败:', error)
    }
  }

  /**
   * 获取用户信息（兼容旧接口）
   */
  async function getUserInfo(): Promise<UserProfile | null> {
    // 直接返回当前用户的profile，不再调用API
    return profile.value
  }

  /**
   * 刷新Token（兼容旧接口）
   */
  async function refreshToken(): Promise<{ success: boolean; token?: string; message?: string }> {
    const apiService = getCurrentApiService()
    if (!apiService) {
      console.warn('没有可用的API服务')
      return { success: false, message: '没有可用的API服务' }
    }

    try {
      const result = await apiService.refreshToken()
      return result
    } catch (error: any) {
      console.error('刷新Token失败:', error)
      return { success: false, message: error.message || '刷新Token失败' }
    }
  }

  /**
   * 验证Token（兼容旧接口）
   */
  async function validateToken(): Promise<boolean> {
    const apiService = getCurrentApiService()
    if (!apiService) {
      console.warn('没有可用的API服务')
      return false
    }

    try {
      return await apiService.validateToken()
    } catch (error) {
      console.error('验证Token失败:', error)
      return false
    }
  }

  return {
    // 状态
    profile,
    token,
    isLoggedIn,
    currentUserId,
    currentUsername,
    allOnlineUsers,
    canAddUser,
    userInfo,

    // 方法
    login,
    logout,
    switchUser,
    getCurrentApiService,
    getCurrentDBService,
    getCurrentShadowRoot,
    getSystemStatus,
    clearAllUsers,
    getUserInfo,
    refreshToken,
    validateToken,
  }
})
