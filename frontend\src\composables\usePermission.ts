import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { Permission, RolePermissions, type UserRole } from '@/types/api/user'

export function usePermission() {
  const userStore = useUserStore()

  // 获取用户权限列表
  const userPermissions = computed(() => {
    const profile = userStore.profile
    if (!profile) return []

    return RolePermissions[profile.role as UserRole] || []
  })

  // 检查用户是否有指定权限
  const hasPermission = (permission: Permission): boolean => {
    return userPermissions.value.includes(permission)
  }

  // 检查用户是否有任一权限
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some((permission) => hasPermission(permission))
  }

  // 检查用户是否有所有权限
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every((permission) => hasPermission(permission))
  }

  // 检查用户角色
  const hasRole = (role: UserRole): boolean => {
    const profile = userStore.profile
    return profile?.role === role
  }

  // 检查是否是管理员
  const isAdmin = computed(() => hasRole('admin'))

  // 检查是否是普通用户
  const isUser = computed(() => hasRole('user'))

  // 检查是否是访客
  const isGuest = computed(() => hasRole('guest'))

  return {
    userPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    isAdmin,
    isUser,
    isGuest,
  }
}
