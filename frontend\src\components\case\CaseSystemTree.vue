<template>
  <div class="case-system-tree">
    <!-- 工具栏 -->
    <div class="tree-toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索用例..."
          :prefix-icon="Search"
          clearable
          size="small"
          style="width: 200px"
          @input="handleSearch"
          @clear="handleClearSearch"
        />
      </div>

      <div class="toolbar-right">
        <el-button
          size="small"
          :icon="Refresh"
          :loading="isLoading"
          @click="handleRefresh"
          title="刷新"
        />
        <el-button
          size="small"
          :icon="Expand"
          @click="handleExpandAll"
          title="展开全部"
        />
        <el-button
          size="small"
          :icon="Fold"
          @click="handleCollapseAll"
          title="折叠全部"
        />
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="tree-stats">
      <el-tag size="small" type="info">
        系统: {{ statistics.totalSystems }}
      </el-tag>
      <el-tag size="small" type="success">
        用例: {{ statistics.totalCases }}
      </el-tag>
      <el-tag v-if="selectedCount > 0" size="small" type="warning">
        已选: {{ selectedCount }}
      </el-tag>
    </div>

    <!-- 树形结构 -->
    <div class="tree-container">
      <el-scrollbar v-if="!isEmpty" height="400px">
        <el-tree
          ref="treeRef"
          :data="filteredSystems"
          :props="treeProps"
          :expand-on-click-node="false"
          :check-on-click-node="true"
          show-checkbox
          node-key="id"
          :default-expanded-keys="Array.from(state.expandedNodes)"
          :default-checked-keys="Array.from(state.selectedNodes)"
          @node-click="handleNodeClick"
          @check="handleNodeCheck"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <div class="node-content">
                <el-icon class="node-icon" :class="`icon-${data.type}`">
                  <component :is="getNodeIcon(data.type)" />
                </el-icon>
                <span class="node-label">{{ data.displayName }}</span>
                <el-tag
                  v-if="data.type === 'case'"
                  size="small"
                  :type="data.selected ? 'success' : 'info'"
                  class="node-tag"
                >
                  {{ data.selected ? '已选' : '未选' }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>

      <!-- 空状态 -->
      <div v-else-if="!isLoading" class="empty-state">
        <el-empty
          :image-size="100"
          description="暂无用例数据"
        >
          <el-button type="primary" @click="handleRefresh">
            重新加载
          </el-button>
        </el-empty>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <el-skeleton :rows="8" animated />
      </div>

      <!-- 错误状态 -->
      <div v-if="hasError" class="error-state">
        <el-alert
          :title="state.error || ''"
          type="error"
          show-icon
          :closable="false"
        >
          <template #default>
            <el-button size="small" type="primary" @click="handleRefresh">
              重试
            </el-button>
          </template>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ElInput,
  ElButton,
  ElTag,
  ElTree,
  ElScrollbar,
  ElEmpty,
  ElSkeleton,
  ElAlert,
  ElIcon,
  ElMessage
} from 'element-plus'
import {
  Search,
  Refresh,
  Expand,
  Fold,
  Folder,
  Document,
  Files,
  DocumentCopy
} from '@element-plus/icons-vue'
import { useCaseManagement } from '@/composables/useCaseManagement'
import type { CaseNode } from '@/services/api'

// 定义事件
const emit = defineEmits<{
  nodeClick: [node: CaseNode]
  nodeSelect: [selectedNodes: CaseNode[]]
  refresh: []
}>()

// 使用用例管理
const {
  state,
  isLoading,
  hasError,
  isEmpty,
  selectedCount,
  filteredSystems,
  fetchSystems,
  refreshSystems,
  searchCases,
  clearSearch,
  toggleNodeSelection,
  clearSelection,
  toggleNodeExpansion,
  expandAll,
  collapseAll,
  getSelectedNodes,
  getStatistics
} = useCaseManagement()

// 本地状态
const treeRef = ref()
const searchKeyword = ref('')

// 计算属性
const statistics = computed(() => getStatistics())

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'displayName'
}

// 获取节点图标
const getNodeIcon = (type: string) => {
  const iconMap = {
    system: Folder,
    function: Files,
    testItem: Document,
    case: DocumentCopy
  }
  return (iconMap as any)[type] || Document
}

// 处理搜索
const handleSearch = (keyword: string) => {
  searchCases(keyword)
}

// 处理清空搜索
const handleClearSearch = () => {
  clearSearch()
  searchKeyword.value = ''
}

// 处理刷新
const handleRefresh = async () => {
  const success = await refreshSystems()
  if (success) {
    emit('refresh')
  }
}

// 处理展开全部
const handleExpandAll = () => {
  expandAll()
  if (treeRef.value) {
    // 更新树组件的展开状态
    const allNodeIds = getAllNodeIds(filteredSystems.value)
    allNodeIds.forEach(id => {
      treeRef.value.setExpanded(id, true)
    })
  }
}

// 处理折叠全部
const handleCollapseAll = () => {
  collapseAll()
  if (treeRef.value) {
    // 更新树组件的展开状态
    const allNodeIds = getAllNodeIds(filteredSystems.value)
    allNodeIds.forEach(id => {
      treeRef.value.setExpanded(id, false)
    })
  }
}

// 获取所有节点ID
const getAllNodeIds = (nodes: CaseNode[]): string[] => {
  const ids: string[] = []

  const collectIds = (nodeList: CaseNode[]) => {
    nodeList.forEach(node => {
      ids.push(node.id)
      if (node.children) {
        collectIds(node.children)
      }
    })
  }

  collectIds(nodes)
  return ids
}

// 处理节点点击
const handleNodeClick = (data: CaseNode) => {
  emit('nodeClick', data)
}

// 处理节点选择
const handleNodeCheck = (data: CaseNode, checked: any) => {
  // 使用提供的方法更新选择状态
  toggleNodeSelection(data.id)

  // 发送选择事件
  emit('nodeSelect', getSelectedNodes())
}

// 处理节点展开
const handleNodeExpand = (data: CaseNode) => {
  toggleNodeExpansion(data.id)
}

// 处理节点折叠
const handleNodeCollapse = (data: CaseNode) => {
  toggleNodeExpansion(data.id)
}

// 组件挂载时加载数据
onMounted(async () => {
  await fetchSystems()
})

// 暴露方法给父组件
defineExpose({
  refresh: handleRefresh,
  expandAll: handleExpandAll,
  collapseAll: handleCollapseAll,
  clearSelection,
  getSelectedNodes
})
</script>

<style scoped>
.case-system-tree {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
}

.tree-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.tree-stats {
  display: flex;
  gap: 8px;
  padding: 8px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.tree-container {
  position: relative;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
}

.icon-system { color: #409eff; }
.icon-function { color: #67c23a; }
.icon-testItem { color: #e6a23c; }
.icon-case { color: #909399; }

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-tag {
  margin-left: auto;
}

.empty-state,
.loading-state,
.error-state {
  padding: 40px 20px;
}

.error-state .el-alert {
  margin-bottom: 16px;
}

/* 树形组件样式调整 */
:deep(.el-tree-node__content) {
  height: 32px;
  padding: 0 8px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node__expand-icon) {
  color: #c0c4cc;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-checkbox) {
  margin-right: 8px;
}
</style>
