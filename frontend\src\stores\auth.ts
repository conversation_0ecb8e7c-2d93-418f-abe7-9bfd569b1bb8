import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import axios from 'axios'
import type { UserProfile, LoginForm, LoginResponse, TokenInfo } from '@/types/api/user'
import { useUserStore } from './user'
import { tokenManager } from '@/utils/tokenManager'

const USER_PROFILE_KEY = 'user_profile'

export const useAuthStore = defineStore('auth', () => {
  // 用户信息
  const userProfile = ref<UserProfile | null>(null)

  // Token信息
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const tokenExpires = ref<string | null>(null)

  // 是否记住登录状态
  const rememberLogin = ref(false)

  // 计算属性：是否已登录
  const isLoggedIn = computed(() => {
    return !!accessToken.value && !!userProfile.value
  })

  // 计算属性：用户角色
  const userRole = computed(() => {
    return userProfile.value?.role || null
  })

  // 计算属性：用户ID
  const userId = computed(() => {
    return userProfile.value?.user_id || null
  })

  // 计算属性：用户名
  const username = computed(() => {
    return userProfile.value?.username || null
  })

  // 获取存储类型
  const getStorage = () => {
    return rememberLogin.value ? localStorage : sessionStorage
  }

  // 保存token到存储（使用统一token管理器）
  const saveTokenToStorage = (tokenInfo: TokenInfo, profile: UserProfile) => {
    // 使用统一token管理器保存token
    tokenManager.setTokens(
      tokenInfo.access_token,
      tokenInfo.refresh_token,
      rememberLogin.value
    )

    // 保存用户信息
    const storage = getStorage()
    storage.setItem(USER_PROFILE_KEY, JSON.stringify(profile))
  }

  // 从存储中加载token（使用统一token管理器）
  const loadTokenFromStorage = () => {
    try {
      // 从统一token管理器获取token
      const accessTokenValue = tokenManager.getAccessToken()
      const refreshTokenValue = tokenManager.getRefreshToken()

      if (accessTokenValue && refreshTokenValue) {
        accessToken.value = accessTokenValue
        refreshToken.value = refreshTokenValue

        // 加载用户信息
        let storage = localStorage
        let profile = storage.getItem(USER_PROFILE_KEY)

        if (!profile) {
          storage = sessionStorage
          profile = storage.getItem(USER_PROFILE_KEY)
        }

        if (profile) {
          userProfile.value = JSON.parse(profile)
          rememberLogin.value = storage === localStorage

          // 计算过期时间
          try {
            const payload = JSON.parse(atob(accessTokenValue.split('.')[1]))
            tokenExpires.value = new Date(payload.exp * 1000).toISOString()
          } catch (_error) {
            // JWT解析失败，使用默认过期时间
            tokenExpires.value = new Date(Date.now() + 30 * 60 * 1000).toISOString()
          }

          return true
        }
      }
    } catch (error) {
      console.error('加载token失败:', error)
      clearStorage()
    }

    return false
  }

  // 清除存储（使用统一token管理器）
  const clearStorage = () => {
    // 使用统一token管理器清除token
    tokenManager.clearTokens()

    // 清除用户信息
    localStorage.removeItem(USER_PROFILE_KEY)
    sessionStorage.removeItem(USER_PROFILE_KEY)

    // 重置状态
    accessToken.value = null
    refreshToken.value = null
    userProfile.value = null
    tokenExpires.value = null
    rememberLogin.value = false
  }

  // 用户登录
  const login = async (
    loginForm: LoginForm & { rememberMe?: boolean },
  ): Promise<{ success: boolean; message?: string }> => {
    try {
      // 设置记住登录状态
      rememberLogin.value = loginForm.rememberMe || false

      // 直接调用后端API - 使用代理路径
      const response = await axios.post<LoginResponse>(
        '/api/v1/multi-user/auth/login',
        {
          username: loginForm.username,
          password: loginForm.password,
        },
      )

      const loginResult = response.data

      if (loginResult.success && loginResult.data) {
        const { user_info, auth_token, expires_at } = loginResult.data

        // 构造token信息
        const tokenInfo: TokenInfo = {
          access_token: auth_token,
          refresh_token: auth_token, // 使用auth_token作为refresh_token
          expires_at: expires_at,
          expires_in: 24 * 60 * 60, // 24小时
          token_type: 'Bearer',
        }

        // 保存到状态
        accessToken.value = auth_token
        refreshToken.value = auth_token
        userProfile.value = user_info
        tokenExpires.value = expires_at

        // 保存到存储
        saveTokenToStorage(tokenInfo, user_info)

        // 同步到原用户系统
        const userStore = useUserStore()
        try {
          await userStore.login({
            username: loginForm.username,
            password: loginForm.password,
          })
        } catch (_userStoreError) {
          // 同步到用户系统失败处理
        }

        return { success: true, message: loginResult.message }
      } else {
        return { success: false, message: loginResult.message || '登录失败' }
      }
    } catch (error: any) {
      let errorMessage = '登录失败'

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      }

      return { success: false, message: errorMessage }
    }
  }

  // 用户登出
  const logout = async (): Promise<{ success: boolean; message?: string }> => {
    try {
      const userStore = useUserStore()

      // 使用原有的多用户系统登出
      const result = await userStore.logout()

      // 清除状态
      accessToken.value = null
      refreshToken.value = null
      userProfile.value = null
      tokenExpires.value = null

      // 清除存储
      clearStorage()

      return result
    } catch (error: any) {
      return { success: false, message: error.message || '登出失败' }
    }
  }

  // 刷新Token（使用统一token管理器）
  const refreshAccessToken = async (): Promise<{
    success: boolean
    token?: string
    message?: string
  }> => {
    try {
      // 使用统一token管理器刷新token
      const tokenResult = await tokenManager.smartRefreshToken()

      if (tokenResult.success && tokenResult.accessToken) {
        // 更新本地状态
        accessToken.value = tokenResult.accessToken

        // 计算过期时间
        try {
          const payload = JSON.parse(atob(tokenResult.accessToken.split('.')[1]))
          tokenExpires.value = new Date(payload.exp * 1000).toISOString()
        } catch (_error) {
          // JWT解析失败，使用默认过期时间
          tokenExpires.value = new Date(Date.now() + 30 * 60 * 1000).toISOString()
        }

        return { success: true, token: tokenResult.accessToken }
      }

      return { success: false, message: tokenResult.message || 'Token刷新失败' }
    } catch (error: any) {
      return { success: false, message: error.message || 'Token刷新失败' }
    }
  }

  // 验证Token
  const validateToken = async (): Promise<boolean> => {
    try {
      if (!accessToken.value) return false

      const userStore = useUserStore()
      return await userStore.validateToken()
    } catch (_error) {
      return false
    }
  }

  // 检查Token是否即将过期
  const isTokenExpiringSoon = (minutes: number = 5): boolean => {
    if (!tokenExpires.value) return false

    const expiresAt = new Date(tokenExpires.value).getTime()
    const now = Date.now()
    const timeUntilExpiration = expiresAt - now

    return timeUntilExpiration <= minutes * 60 * 1000
  }

  // 检查Token是否过期
  const isTokenExpired = (): boolean => {
    if (!tokenExpires.value) return true

    const expiresAt = new Date(tokenExpires.value).getTime()
    return Date.now() >= expiresAt
  }

  // 获取Token剩余时间（分钟）
  const getTokenRemainingTime = (): number => {
    if (!tokenExpires.value) return 0

    const expiresAt = new Date(tokenExpires.value).getTime()
    const now = Date.now()
    const remaining = expiresAt - now

    return Math.max(0, Math.floor(remaining / (60 * 1000)))
  }

  // 初始化
  const init = () => {
    loadTokenFromStorage()

    // 启动自动token刷新监控
    tokenManager.startAutoRefresh()

    // 监听认证失败事件
    tokenManager.on('authenticationFailed', (data: any) => {
      console.error('认证失败:', data.message)
      clearStorage()
      // 可以在这里添加跳转到登录页的逻辑
    })
  }

  // 监听token变化，自动同步到原用户系统
  watch([accessToken, userProfile], () => {
    // 这里可以添加同步逻辑
  })

  return {
    // 状态
    userProfile,
    accessToken,
    refreshToken,
    tokenExpires,
    rememberLogin,

    // 计算属性
    isLoggedIn,
    userRole,
    userId,
    username,

    // 方法
    login,
    logout,
    refreshAccessToken,
    validateToken,
    isTokenExpiringSoon,
    isTokenExpired,
    getTokenRemainingTime,
    init,

    // 存储管理
    saveTokenToStorage,
    loadTokenFromStorage,
    clearStorage,
  }
})
