/**
 * 通用API响应体
 */
export interface BaseResponse<T = any> {
  status: 'success' | 'error'
  message: string
  data: T
  timestamp?: string
  request_id?: string
}

/**
 * 标准API响应接口（兼容现有代码）
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp?: string
  request_id?: string
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number
  page_size: number
}

/**
 * 分页响应体
 */
export interface PaginationResponse<T> {
  items: T
  total: number
  page: number
  page_size: number
  total_pages: number
}
