<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

// 初始化Auth Store
const authStore = useAuthStore()

// 应用初始化
onMounted(() => {
  // 初始化认证状态
  authStore.init()
})
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
#app {
  margin: 0;
  padding: 0;
  height: 100vh;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

/* Element Plus 自定义样式 */
.el-menu {
  border-right: none !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 权限相关样式 */
.permission-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.permission-hidden {
  display: none !important;
}

/* 网络状态指示器 */
.network-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  transition: all 0.3s ease;
}

.network-indicator.offline {
  background-color: #f56c6c;
}

.network-indicator.online {
  background-color: #67c23a;
}

/* Token状态指示器 */
.token-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  transition: all 0.3s ease;
}

.token-status.warning {
  background-color: #e6a23c;
}

.token-status.danger {
  background-color: #f56c6c;
}

.token-status.success {
  background-color: #67c23a;
}
</style>
