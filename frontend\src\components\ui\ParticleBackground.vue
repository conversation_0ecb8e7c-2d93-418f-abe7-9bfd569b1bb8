<template>
  <div class="particle-background" :style="containerStyle">
    <canvas ref="canvasRef" class="particle-canvas" :width="canvasWidth" :height="canvasHeight" />

    <!-- 装饰性几何图形 -->
    <div class="geometric-shapes">
      <div
        v-for="(shape, index) in geometricShapes"
        :key="index"
        class="shape"
        :class="shape.type"
        :style="shape.style"
      />
    </div>

    <!-- 光晕效果 -->
    <div class="glow-effects">
      <div v-for="(glow, index) in glowEffects" :key="index" class="glow" :style="glow.style" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'

interface Props {
  particleCount?: number
  particleSpeed?: number
  particleSize?: number
  colors?: string[]
  autoPlay?: boolean
  canvasWidth?: number
  canvasHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  particleCount: 50,
  particleSpeed: 1,
  particleSize: 2,
  colors: () => ['#FF6B6B', '#4ECDC4', '#FFE66D', '#00D2FF', '#8B5CF6'],
  autoPlay: true,
  canvasWidth: 1920,
  canvasHeight: 1080,
})

// Canvas引用
const canvasRef = ref<HTMLCanvasElement>()

// 粒子系统
interface Particle {
  x: number
  y: number
  vx: number
  vy: number
  size: number
  color: string
  opacity: number
  life: number
  maxLife: number
}

const particles = ref<Particle[]>([])
const animationId = ref<number>()

// 计算属性
const containerStyle = computed(() => ({
  width: '100%',
  height: '100%',
}))

const canvasWidth = computed(() => props.canvasWidth)
const canvasHeight = computed(() => props.canvasHeight)

// 几何装饰形状
const geometricShapes = ref([
  {
    type: 'circle',
    style: {
      top: '10%',
      left: '5%',
      width: '100px',
      height: '100px',
      background: 'radial-gradient(circle, rgba(255, 107, 107, 0.3) 0%, transparent 70%)',
      animationDelay: '0s',
    },
  },
  {
    type: 'triangle',
    style: {
      top: '20%',
      right: '10%',
      width: '0',
      height: '0',
      borderLeft: '50px solid transparent',
      borderRight: '50px solid transparent',
      borderBottom: '86px solid rgba(78, 205, 196, 0.2)',
      animationDelay: '2s',
    },
  },
  {
    type: 'square',
    style: {
      bottom: '15%',
      left: '15%',
      width: '80px',
      height: '80px',
      background: 'linear-gradient(45deg, rgba(255, 230, 109, 0.3), rgba(0, 210, 255, 0.3))',
      transform: 'rotate(45deg)',
      animationDelay: '4s',
    },
  },
  {
    type: 'hexagon',
    style: {
      top: '60%',
      right: '20%',
      width: '60px',
      height: '60px',
      background: 'rgba(139, 92, 246, 0.2)',
      clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
      animationDelay: '6s',
    },
  },
])

// 光晕效果
const glowEffects = ref([
  {
    style: {
      top: '30%',
      left: '20%',
      width: '200px',
      height: '200px',
      background: 'radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%)',
      animationDelay: '1s',
    },
  },
  {
    style: {
      top: '50%',
      right: '30%',
      width: '150px',
      height: '150px',
      background: 'radial-gradient(circle, rgba(0, 210, 255, 0.1) 0%, transparent 70%)',
      animationDelay: '3s',
    },
  },
  {
    style: {
      bottom: '20%',
      left: '40%',
      width: '180px',
      height: '180px',
      background: 'radial-gradient(circle, rgba(255, 107, 107, 0.1) 0%, transparent 70%)',
      animationDelay: '5s',
    },
  },
])

// 初始化粒子
const initParticles = () => {
  if (!canvasRef.value) return
  particles.value = []
  const techColors = ['#64FFDA', '#8892B0', '#CCD6F6'] // 青色, 石板灰, 亮灰蓝
  for (let i = 0; i < props.particleCount; i++) {
    particles.value.push({
      x: Math.random() * canvasRef.value.width,
      y: Math.random() * canvasRef.value.height,
      vx: (Math.random() - 0.5) * props.particleSpeed,
      vy: (Math.random() - 0.5) * props.particleSpeed,
      size: Math.random() * props.particleSize + 1,
      color: techColors[Math.floor(Math.random() * techColors.length)],
      opacity: Math.random() * 0.8 + 0.2,
      life: Math.random() * 100,
      maxLife: 100,
    })
  }
}

// 更新粒子
const updateParticles = () => {
  particles.value.forEach((particle) => {
    // 更新位置
    particle.x += particle.vx
    particle.y += particle.vy

    // 边界检测
    if (particle.x < 0 || particle.x > canvasWidth.value) {
      particle.vx *= -1
    }
    if (particle.y < 0 || particle.y > canvasHeight.value) {
      particle.vy *= -1
    }

    // 更新生命周期
    particle.life += 0.5
    if (particle.life > particle.maxLife) {
      particle.life = 0
      particle.x = Math.random() * canvasWidth.value
      particle.y = Math.random() * canvasHeight.value
    }
  })
}

// 绘制粒子
const drawParticles = () => {
  const canvas = canvasRef.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清除画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

  // 绘制粒子
  particles.value.forEach((particle) => {
    ctx.save()
    ctx.globalAlpha = particle.opacity * (1 - particle.life / particle.maxLife)
    ctx.fillStyle = particle.color
    ctx.shadowColor = particle.color
    ctx.shadowBlur = 10
    ctx.beginPath()
    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
    ctx.fill()
    ctx.restore()
  })

  // 绘制粒子连线
  drawParticleConnections(ctx)
}

// 绘制粒子连线
const drawParticleConnections = (ctx: CanvasRenderingContext2D) => {
  const maxDistance = 150

  for (let i = 0; i < particles.value.length; i++) {
    for (let j = i + 1; j < particles.value.length; j++) {
      const dx = particles.value[i].x - particles.value[j].x
      const dy = particles.value[i].y - particles.value[j].y
      const distance = Math.sqrt(dx * dx + dy * dy)

      if (distance < maxDistance) {
        const opacity = (1 - distance / maxDistance) * 0.3
        ctx.save()
        ctx.globalAlpha = opacity
        ctx.strokeStyle = '#FFD700'
        ctx.lineWidth = 1
        ctx.beginPath()
        ctx.moveTo(particles.value[i].x, particles.value[i].y)
        ctx.lineTo(particles.value[j].x, particles.value[j].y)
        ctx.stroke()
        ctx.restore()
      }
    }
  }
}

// 动画循环
const animate = () => {
  updateParticles()
  drawParticles()
  animationId.value = requestAnimationFrame(animate)
}

// 开始动画
const startAnimation = () => {
  if (props.autoPlay) {
    animate()
  }
}

// 停止动画
const stopAnimation = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
}

// 组件挂载
onMounted(() => {
  initParticles()
  startAnimation()

  // 监听窗口大小变化
  const handleResize = () => {
    // 可以在这里添加响应式处理
  }

  window.addEventListener('resize', handleResize)

  // 清理函数
  onUnmounted(() => {
    stopAnimation()
    window.removeEventListener('resize', handleResize)
  })
})

// 监听属性变化
watch(
  () => props.particleCount,
  () => {
    initParticles()
  },
)

watch(
  () => props.autoPlay,
  (newVal) => {
    if (newVal) {
      startAnimation()
    } else {
      stopAnimation()
    }
  },
)
</script>

<style scoped lang="scss">
@use '@/assets/styles/theme.scss' as theme;

.particle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;

  .particle-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .geometric-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;

    .shape {
      position: absolute;
      animation: float 8s ease-in-out infinite;

      &.circle {
        border-radius: 50%;
      }

      &.triangle {
        width: 0;
        height: 0;
      }

      &.square {
        border-radius: 4px;
      }

      &.hexagon {
        // 使用clip-path定义的六边形
      }
    }
  }

  .glow-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;

    .glow {
      position: absolute;
      border-radius: 50%;
      animation: pulse 6s ease-in-out infinite;
      filter: blur(20px);
    }
  }
}

// 动画定义
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.6;
  }
}

// 响应式设计
@media (max-width: theme.$breakpoint-mobile) {
  .particle-background {
    .geometric-shapes .shape {
      transform: scale(0.7);
    }

    .glow-effects .glow {
      transform: scale(0.8);
    }
  }
}
</style>
