/**
 * 测试新的登出接口
 * 这个脚本用于验证登出接口的请求格式是否正确
 */

// 模拟登出接口调用
async function testLogoutAPI() {
  const testToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2ODcwNmYxOTM5MDllNzQ4YmViNTJmNTciLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiZXhwIjoxNzUzOTg4ODk4LjY5MzM3NjMsImlhdCI6MTc1MzkzMTI5OC42OTMzNzYzLCJqdGkiOiI2ODcwNmYxOTM5MDllNzQ4YmViNTJmNTdfMTc1MzkzMTI5OCJ9.y7bFpYH-9UXjC3vk8v-01aSD5xZywYICiyna98_RT4U"
  
  console.log('测试登出接口...')
  console.log('请求URL: POST /api/v1/multi-user/auth/logout')
  console.log('请求头:')
  console.log('  Content-Type: application/json')
  console.log('  Authorization: Bearer ' + testToken.substring(0, 50) + '...')
  console.log('请求体:')
  console.log(JSON.stringify({
    auth_token: testToken
  }, null, 2))
  
  console.log('\n期望的响应格式:')
  console.log(JSON.stringify({
    "status": "success",
    "message": "登出成功",
    "data": {
      "username": "admin"
    },
    "timestamp": "2025-07-31T11:08:42.509110",
    "request_id": null
  }, null, 2))
  
  console.log('\n修改的文件:')
  console.log('1. frontend/src/views/ProfileCenter.vue - 修改密码成功后调用新登出接口')
  console.log('2. frontend/src/layouts/MainLayout.vue - 退出登录按钮调用新登出接口')
  console.log('3. frontend/src/services/api.ts - 更新API服务中的登出接口')
  console.log('4. frontend/src/services/isolatedApiService.ts - 更新隔离API服务中的登出接口')
  console.log('5. frontend/src/composables/useMultiUserSystem.ts - 更新多用户系统的登出方法')
  
  console.log('\n测试完成！')
}

testLogoutAPI()
