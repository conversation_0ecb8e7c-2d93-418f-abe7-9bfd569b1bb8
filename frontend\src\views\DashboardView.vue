<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎使用用例管理平台</h1>
        <p class="welcome-subtitle">高效管理您的测试用例，提升测试质量</p>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <h2 class="section-title">快速操作</h2>
      <div class="action-cards">
        <div class="action-card" @click="navigateTo('/app/management/usecase')">
          <div class="card-icon">
            <i class="icon-usecase">📋</i>
          </div>
          <div class="card-content">
            <h3 class="card-title">用例管理</h3>
            <p class="card-description">管理和编辑测试用例</p>
          </div>
        </div>

        <div class="action-card" @click="navigateTo('/app/statistics/dashboard')">
          <div class="card-icon">
            <i class="icon-stats">📊</i>
          </div>
          <div class="card-content">
            <h3 class="card-title">统计分析</h3>
            <p class="card-description">查看测试统计数据</p>
          </div>
        </div>

        <div class="action-card" @click="navigateTo('/app/review/usecase')">
          <div class="card-icon">
            <i class="icon-review">✓</i>
          </div>
          <div class="card-content">
            <h3 class="card-title">用例评审</h3>
            <p class="card-description">评审测试用例质量</p>
          </div>
        </div>

        <div class="action-card" @click="navigateTo('/app/ai/chat')">
          <div class="card-icon">
            <i class="icon-ai">🤖</i>
          </div>
          <div class="card-content">
            <h3 class="card-title">AI 助手</h3>
            <p class="card-description">智能测试用例生成</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动区域 -->
    <div class="recent-activities">
      <h2 class="section-title">最近活动</h2>
      <div class="activity-grid">
        <div class="activity-list">
          <div class="activity-item">
            <div class="activity-icon">
              <i class="icon-update">📝</i>
            </div>
            <div class="activity-content">
              <h4 class="activity-title">欢迎使用用例管理平台</h4>
              <p class="activity-description">开始您的测试用例管理之旅</p>
              <span class="activity-time">刚刚</span>
            </div>
          </div>
          <div class="activity-item">
            <div class="activity-icon">
              <i class="icon-create">📋</i>
            </div>
            <div class="activity-content">
              <h4 class="activity-title">系统初始化完成</h4>
              <p class="activity-description">多用户系统已就绪，支持10用户同时在线</p>
              <span class="activity-time">5分钟前</span>
            </div>
          </div>
          <div class="activity-item">
            <div class="activity-icon">
              <i class="icon-check">✅</i>
            </div>
            <div class="activity-content">
              <h4 class="activity-title">数据库连接成功</h4>
              <p class="activity-description">远程服务器连接已建立</p>
              <span class="activity-time">10分钟前</span>
            </div>
          </div>
        </div>

        <div class="stats-summary">
          <h3 class="stats-title">平台统计</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">0</div>
              <div class="stat-label">测试用例</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">1</div>
              <div class="stat-label">在线用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">0</div>
              <div class="stat-label">今日评审</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">100%</div>
              <div class="stat-label">系统可用性</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">5</div>
              <div class="stat-label">活跃项目</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">15</div>
              <div class="stat-label">待办任务</div>
            </div>
          </div>

          <!-- 快捷操作区域 -->
          <div class="quick-stats">
            <h4 class="quick-stats-title">快捷功能</h4>
            <div class="quick-stats-list">
              <div class="quick-stat-item" @click="navigateTo('/app/management/usecase')">
                <span class="quick-stat-icon">📝</span>
                <span class="quick-stat-text">新建用例</span>
              </div>
              <div class="quick-stat-item" @click="navigateTo('/app/review/usecase')">
                <span class="quick-stat-icon">👀</span>
                <span class="quick-stat-text">开始评审</span>
              </div>
              <div class="quick-stat-item" @click="navigateTo('/app/statistics/dashboard')">
                <span class="quick-stat-icon">📊</span>
                <span class="quick-stat-text">查看报告</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统状态区域 -->
    <div class="system-status">
      <h2 class="section-title">系统状态</h2>
      <div class="status-grid">
        <div class="status-card">
          <div class="status-header">
            <h4 class="status-title">服务状态</h4>
            <span class="status-indicator online">在线</span>
          </div>
          <div class="status-content">
            <div class="status-item">
              <span class="status-label">API服务</span>
              <span class="status-value">✅ 正常</span>
            </div>
            <div class="status-item">
              <span class="status-label">数据库</span>
              <span class="status-value">✅ 连接正常</span>
            </div>
            <div class="status-item">
              <span class="status-label">认证服务</span>
              <span class="status-value">✅ 运行中</span>
            </div>
          </div>
        </div>

        <div class="status-card">
          <div class="status-header">
            <h4 class="status-title">多用户系统</h4>
            <span class="status-indicator active">活跃</span>
          </div>
          <div class="status-content">
            <div class="status-item">
              <span class="status-label">最大用户数</span>
              <span class="status-value">10</span>
            </div>
            <div class="status-item">
              <span class="status-label">当前在线</span>
              <span class="status-value">1</span>
            </div>
            <div class="status-item">
              <span class="status-label">剩余容量</span>
              <span class="status-value">9</span>
            </div>
          </div>
        </div>

        <div class="status-card">
          <div class="status-header">
            <h4 class="status-title">性能监控</h4>
            <span class="status-indicator good">优秀</span>
          </div>
          <div class="status-content">
            <div class="status-item">
              <span class="status-label">响应时间</span>
              <span class="status-value">&lt;100ms</span>
            </div>
            <div class="status-item">
              <span class="status-label">内存使用</span>
              <span class="status-value">45%</span>
            </div>
            <div class="status-item">
              <span class="status-label">CPU使用</span>
              <span class="status-value">23%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

// 路由
const router = useRouter()

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  max-width: none;
}

.welcome-section {
  background: linear-gradient(135deg, #f0f2f0 0%, #e8ebe8 100%);
  border-radius: 16px;
  padding: 40px 20px;
  margin-bottom: 32px;
  text-align: center;

  .welcome-content {
    .welcome-title {
      font-size: 32px;
      font-weight: 600;
      color: #5a6b5d;
      margin: 0 0 12px 0;
    }

    .welcome-subtitle {
      font-size: 18px;
      color: #8a9688;
      margin: 0;
    }
  }
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #5a6b5d;
  margin-bottom: 20px;
}

.quick-actions {
  margin-bottom: 32px;

  .action-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    width: 100%;
  }

  .action-card {
    background: #fafbfa;
    border: 1px solid #e0e4e0;
    border-radius: 12px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    min-height: 160px;
    text-align: center;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(141, 180, 160, 0.15);
      border-color: #8db4a0;
    }

    .card-icon {
      font-size: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #b8c5a6 0%, #a8b896 100%);
      border-radius: 16px;
      color: white;
    }

    .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #5a6b5d;
        margin: 0 0 8px 0;
        white-space: nowrap;
      }

      .card-description {
        font-size: 14px;
        color: #8a9688;
        margin: 0;
        line-height: 1.4;
      }
    }
  }
}

.recent-activities {
  .activity-grid {
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    gap: 20px;
    width: 100%;
  }

  .activity-list {
    background: #fafbfa;
    border: 1px solid #e0e4e0;
    border-radius: 12px;
    padding: 20px;
  }

  .activity-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 12px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #e0e4e0;
    }

    .activity-icon {
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #b8c5a6 0%, #a8b896 100%);
      border-radius: 8px;
      color: white;
    }

    .activity-content {
      flex: 1;

      .activity-title {
        font-size: 16px;
        font-weight: 600;
        color: #5a6b5d;
        margin: 0 0 4px 0;
      }

      .activity-description {
        font-size: 14px;
        color: #8a9688;
        margin: 0 0 4px 0;
      }

      .activity-time {
        font-size: 12px;
        color: #9ba69d;
      }
    }
  }

  .stats-summary {
    background: #fafbfa;
    border: 1px solid #e0e4e0;
    border-radius: 12px;
    padding: 20px;

    .stats-title {
      font-size: 18px;
      font-weight: 600;
      color: #5a6b5d;
      margin: 0 0 16px 0;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 20px;
    }

    .stat-item {
      text-align: center;
      padding: 16px;
      background: #f0f2f0;
      border-radius: 8px;

      .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: #5a6b5d;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 11px;
        color: #8a9688;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .quick-stats {
      .quick-stats-title {
        font-size: 16px;
        font-weight: 600;
        color: #5a6b5d;
        margin: 0 0 12px 0;
      }

      .quick-stats-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .quick-stat-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        background: #f0f2f0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #e8ebe8;
          transform: translateX(2px);
        }

        .quick-stat-icon {
          font-size: 16px;
        }

        .quick-stat-text {
          font-size: 14px;
          color: #5a6b5d;
          font-weight: 500;
        }
      }
    }
  }
}

.system-status {
  .status-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .status-card {
    background: #fafbfa;
    border: 1px solid #e0e4e0;
    border-radius: 12px;
    padding: 20px;

    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .status-title {
        font-size: 16px;
        font-weight: 600;
        color: #5a6b5d;
        margin: 0;
      }

      .status-indicator {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;

        &.online {
          background: #d4edda;
          color: #155724;
        }

        &.active {
          background: #cce7ff;
          color: #004085;
        }

        &.good {
          background: #d1ecf1;
          color: #0c5460;
        }
      }
    }

    .status-content {
      .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e0e4e0;

        &:last-child {
          border-bottom: none;
        }

        .status-label {
          font-size: 14px;
          color: #8a9688;
        }

        .status-value {
          font-size: 14px;
          font-weight: 500;
          color: #5a6b5d;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .action-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .system-status {
    .status-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 992px) {
  .action-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .recent-activities {
    .activity-grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .welcome-section {
    padding: 24px;
  }

  .action-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .action-card {
    min-height: 140px;
    padding: 20px;

    .card-icon {
      width: 48px;
      height: 48px;
      font-size: 24px;
    }

    .card-content {
      .card-title {
        font-size: 16px;
      }

      .card-description {
        font-size: 13px;
      }
    }
  }

  .recent-activities {
    .activity-grid {
      grid-template-columns: 1fr;
    }

    .stats-summary {
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }

  .system-status {
    .status-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}

@media (max-width: 480px) {
  .action-cards {
    grid-template-columns: 1fr;
  }
}
</style>
