import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebarCollapsed = ref<boolean>(false)
  const currentPageTitle = ref<string>('用例管理平台')
  const loading = ref<boolean>(false)
  const theme = ref<'light' | 'dark'>('light')

  // 面包屑导航
  const breadcrumbItems = ref<Array<{ title: string; route?: string }>>([])

  // 计算属性
  const isLoading = computed(() => loading.value)

  // 方法
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebar_collapsed', String(collapsed))
  }

  const setCurrentPageTitle = (title: string) => {
    currentPageTitle.value = title
    // 更新文档标题
    document.title = `${title} - 用例管理平台`
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    // 更新HTML类名
    document.documentElement.className = newTheme
  }

  const setBreadcrumb = (items: Array<{ title: string; route?: string }>) => {
    breadcrumbItems.value = items
  }

  const addBreadcrumbItem = (item: { title: string; route?: string }) => {
    breadcrumbItems.value.push(item)
  }

  const clearBreadcrumb = () => {
    breadcrumbItems.value = []
  }

  // 初始化应用设置
  const initializeApp = () => {
    // 恢复侧边栏状态
    const savedCollapsed = localStorage.getItem('sidebar_collapsed')
    if (savedCollapsed !== null) {
      sidebarCollapsed.value = savedCollapsed === 'true'
    }

    // 恢复主题设置
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark'
    if (savedTheme) {
      setTheme(savedTheme)
    }
  }

  // 重置应用状态
  const resetApp = () => {
    sidebarCollapsed.value = false
    currentPageTitle.value = '用例管理平台'
    loading.value = false
    breadcrumbItems.value = []
    theme.value = 'light'
  }

  return {
    // 状态
    sidebarCollapsed,
    currentPageTitle,
    loading,
    theme,
    breadcrumbItems,

    // 计算属性
    isLoading,

    // 方法
    setSidebarCollapsed,
    setCurrentPageTitle,
    setLoading,
    setTheme,
    setBreadcrumb,
    addBreadcrumbItem,
    clearBreadcrumb,
    initializeApp,
    resetApp,
  }
})
