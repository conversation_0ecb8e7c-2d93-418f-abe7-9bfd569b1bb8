import { apiService } from './api'
import type { LoginResponse } from '@/types/api/user'

// 定义登录请求的数据结构
interface LoginCredentials {
  username: string
  password: string
}

export const userService = {
  /**
   * 用户登录
   * @param credentials - 用户名和密码
   * @returns 包含用户信息和token的响应
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse | null> {
    try {
      // 使用统一的API服务
      const response = await apiService.auth.login(credentials)
      return response as unknown as LoginResponse
    } catch (_error) {
      return null
    }
  },

  /**
   * 用户登出
   * @param token - 认证token
   */
  async logout(): Promise<void> {
    try {
      await apiService.auth.logout()
    } catch (_error) {
      // 登出失败通常不需要特别处理，因为客户端会清理状态
    }
  },
}
