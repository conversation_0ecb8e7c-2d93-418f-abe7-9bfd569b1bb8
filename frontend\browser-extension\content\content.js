// 内容脚本 - 用例管理平台助手
class FloatingWindowManager {
  constructor() {
    this.container = null;
    this.iframe = null;
    this.isVisible = false;
    this.isDragging = false;
    this.isResizing = false;
    this.config = {
      enabled: true,
      position: { x: 50, y: 50 },
      size: { width: 400, height: 300 },
      transparency: 0.9,
      alwaysOnTop: true
    };
    this.originalPosition = { x: 0, y: 0 };
    this.dragOffset = { x: 0, y: 0 };
    this.observers = [];
    this.extensionId = chrome.runtime.id;
    this.instanceId = null;
    this.persistentConfig = null;

    this.init();
  }

  async init() {
    console.log('🚀 悬浮窗管理器初始化');

    try {
      // 等待DOM完全加载
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.initializeWindow());
      } else {
        this.initializeWindow();
      }

      // 监听来自后台脚本的消息
      chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));

      // 监听页面变化
      this.setupPageObservers();

      // 注册页面实例
      await this.registerPageInstance();

      // 监听页面可见性变化
      this.setupVisibilityMonitor();

    } catch (error) {
      console.error('悬浮窗初始化失败:', error);
    }
  }

  async initializeWindow() {
    try {
      // 从持久化管理器获取配置
      const response = await chrome.runtime.sendMessage({
        type: 'INIT_PERSISTENT_FLOATING_WINDOW',
        url: window.location.href
      });

      if (response.success) {
        this.config = { ...this.config, ...response.config };
        this.persistentConfig = response.config;
        this.instanceId = response.instanceId;

        // 如果有保存的窗口状态，使用它
        if (response.windowState) {
          this.config.position = response.windowState.position;
          this.config.size = response.windowState.size;
          this.config.transparency = response.windowState.transparency;
          this.isVisible = response.windowState.visible;
        }

        this.createPersistentFloatingWindow();
      }
    } catch (error) {
      console.error('获取配置失败:', error);
      // 使用默认配置创建窗口
      this.createPersistentFloatingWindow();
    }
  }

  createFloatingWindow() {
    if (this.container) {
      return; // 已经创建了
    }

    // 创建容器
    this.container = document.createElement('div');
    this.container.id = 'usecase-platform-floating-window';
    this.container.className = 'usecase-floating-container';

    // 设置容器样式
    this.applyContainerStyles();

    // 创建工具栏
    this.createToolbar();

    // 创建iframe
    this.createIframe();

    // 创建调整大小手柄
    this.createResizeHandle();

    // 添加到页面
    document.body.appendChild(this.container);

    // 设置事件监听
    this.setupEventListeners();

    // 确保永久可见
    this.ensureAlwaysVisible();

    console.log('✅ 悬浮窗创建完成');

    // 通知后台脚本
    chrome.runtime.sendMessage({
      type: 'FLOATING_WINDOW_READY',
      url: window.location.href
    });
  }

  applyContainerStyles() {
    Object.assign(this.container.style, {
      position: 'fixed',
      top: `${this.config.position.y}px`,
      left: `${this.config.position.x}px`,
      width: `${this.config.size.width}px`,
      height: `${this.config.size.height}px`,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      border: '1px solid rgba(0, 0, 0, 0.2)',
      borderRadius: '8px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
      zIndex: '2147483647', // 最大z-index
      fontFamily: 'Arial, sans-serif',
      fontSize: '14px',
      opacity: this.config.transparency,
      display: this.config.enabled ? 'block' : 'none',
      overflow: 'hidden',
      backdropFilter: 'blur(10px)',
      transition: 'all 0.3s ease'
    });
  }

  createToolbar() {
    const toolbar = document.createElement('div');
    toolbar.className = 'usecase-floating-toolbar';
    toolbar.innerHTML = `
      <div class="toolbar-title">
        <span>用例管理平台</span>
      </div>
      <div class="toolbar-controls">
        <button class="toolbar-btn" id="minimize-btn" title="最小化">−</button>
        <button class="toolbar-btn" id="maximize-btn" title="最大化">□</button>
        <button class="toolbar-btn" id="close-btn" title="关闭">×</button>
      </div>
    `;

    // 工具栏样式
    Object.assign(toolbar.style, {
      height: '30px',
      backgroundColor: 'rgba(59, 130, 246, 0.9)',
      color: 'white',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '0 10px',
      cursor: 'move',
      borderRadius: '8px 8px 0 0',
      userSelect: 'none'
    });

    // 按钮样式
    const style = document.createElement('style');
    style.textContent = `
      .usecase-floating-toolbar .toolbar-btn {
        background: none;
        border: none;
        color: white;
        font-size: 16px;
        cursor: pointer;
        padding: 2px 6px;
        margin-left: 5px;
        border-radius: 3px;
        transition: background-color 0.2s;
      }
      .usecase-floating-toolbar .toolbar-btn:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
      .usecase-floating-toolbar .toolbar-title {
        font-size: 12px;
        font-weight: 500;
      }
    `;
    document.head.appendChild(style);

    this.container.appendChild(toolbar);
    this.toolbar = toolbar;
  }

  createIframe() {
    this.iframe = document.createElement('iframe');
    this.iframe.src = chrome.runtime.getURL('content/iframe.html');
    this.iframe.id = 'usecase-platform-iframe';

    // iframe样式
    Object.assign(this.iframe.style, {
      width: '100%',
      height: 'calc(100% - 30px)',
      border: 'none',
      borderRadius: '0 0 8px 8px',
      backgroundColor: 'white'
    });

    // 设置沙盒属性
    this.iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms');

    this.container.appendChild(this.iframe);

    // 监听iframe加载完成
    this.iframe.onload = () => {
      console.log('✅ 悬浮窗iframe加载完成');
      this.setupIframeMessaging();
    };
  }

  createResizeHandle() {
    const handle = document.createElement('div');
    handle.className = 'usecase-resize-handle';

    Object.assign(handle.style, {
      position: 'absolute',
      right: '0',
      bottom: '0',
      width: '15px',
      height: '15px',
      cursor: 'nw-resize',
      background: 'linear-gradient(-45deg, transparent 40%, rgba(0,0,0,0.3) 50%, transparent 60%)',
      borderRadius: '0 0 8px 0'
    });

    this.container.appendChild(handle);
    this.resizeHandle = handle;
  }

  setupEventListeners() {
    // 工具栏按钮事件
    const minimizeBtn = this.container.querySelector('#minimize-btn');
    const maximizeBtn = this.container.querySelector('#maximize-btn');
    const closeBtn = this.container.querySelector('#close-btn');

    minimizeBtn.addEventListener('click', () => this.minimize());
    maximizeBtn.addEventListener('click', () => this.toggleMaximize());
    closeBtn.addEventListener('click', () => this.hide());

    // 拖拽事件
    this.toolbar.addEventListener('mousedown', this.startDrag.bind(this));
    document.addEventListener('mousemove', this.drag.bind(this));
    document.addEventListener('mouseup', this.stopDrag.bind(this));

    // 调整大小事件
    this.resizeHandle.addEventListener('mousedown', this.startResize.bind(this));
    document.addEventListener('mousemove', this.resize.bind(this));
    document.addEventListener('mouseup', this.stopResize.bind(this));

    // 防止点击穿透
    this.container.addEventListener('click', (e) => {
      e.stopPropagation();
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.altKey && e.key === 'u') {
        this.toggle();
      }
    });
  }

  setupIframeMessaging() {
    // 监听来自iframe的消息
    window.addEventListener('message', (event) => {
      if (event.source !== this.iframe.contentWindow) return;

      const { type, data } = event.data;

      switch (type) {
        case 'IFRAME_READY':
          this.sendToIframe('INIT_CONFIG', {
            config: this.config,
            parentUrl: window.location.href,
            extensionId: this.extensionId
          });
          break;

        case 'API_REQUEST':
          this.handleApiRequest(data);
          break;

        case 'RESIZE_WINDOW':
          this.resizeWindow(data.width, data.height);
          break;

        case 'CHANGE_TRANSPARENCY':
          this.changeTransparency(data.transparency);
          break;
      }
    });
  }

  sendToIframe(type, data) {
    if (this.iframe && this.iframe.contentWindow) {
      this.iframe.contentWindow.postMessage({ type, data }, '*');
    }
  }

  async handleApiRequest(requestData) {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'API_REQUEST',
        ...requestData
      });

      this.sendToIframe('API_RESPONSE', {
        requestId: requestData.requestId,
        response
      });
    } catch (error) {
      this.sendToIframe('API_ERROR', {
        requestId: requestData.requestId,
        error: error.message
      });
    }
  }

  startDrag(e) {
    if (e.target.classList.contains('toolbar-btn')) return;

    this.isDragging = true;
    this.dragOffset = {
      x: e.clientX - this.container.offsetLeft,
      y: e.clientY - this.container.offsetTop
    };

    this.container.style.cursor = 'grabbing';
    e.preventDefault();
  }

  drag(e) {
    if (!this.isDragging) return;

    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;

    // 限制在窗口边界内
    const maxX = window.innerWidth - this.container.offsetWidth;
    const maxY = window.innerHeight - this.container.offsetHeight;

    const boundedX = Math.max(0, Math.min(x, maxX));
    const boundedY = Math.max(0, Math.min(y, maxY));

    this.container.style.left = `${boundedX}px`;
    this.container.style.top = `${boundedY}px`;

    this.config.position = { x: boundedX, y: boundedY };
  }

  stopDrag() {
    if (this.isDragging) {
      this.isDragging = false;
      this.container.style.cursor = 'default';

      // 保存状态到持久化存储
      this.saveWindowState();
    }
  }

  startResize(e) {
    this.isResizing = true;
    this.originalPosition = {
      x: e.clientX,
      y: e.clientY
    };
    e.preventDefault();
  }

  resize(e) {
    if (!this.isResizing) return;

    const deltaX = e.clientX - this.originalPosition.x;
    const deltaY = e.clientY - this.originalPosition.y;

    const newWidth = Math.max(300, this.config.size.width + deltaX);
    const newHeight = Math.max(200, this.config.size.height + deltaY);

    this.resizeWindow(newWidth, newHeight);

    this.originalPosition = {
      x: e.clientX,
      y: e.clientY
    };
  }

  stopResize() {
    if (this.isResizing) {
      this.isResizing = false;

      // 保存状态到持久化存储
      this.saveWindowState();
    }
  }

  resizeWindow(width, height) {
    this.config.size = { width, height };
    this.container.style.width = `${width}px`;
    this.container.style.height = `${height}px`;
  }

  changeTransparency(transparency) {
    this.config.transparency = transparency;
    this.container.style.opacity = transparency;
  }

  show() {
    this.container.style.display = 'block';
    this.isVisible = true;
    this.config.enabled = true;
  }

  hide() {
    this.container.style.display = 'none';
    this.isVisible = false;
    this.config.enabled = false;
  }

  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  minimize() {
    this.container.style.height = '30px';
    this.iframe.style.display = 'none';
    this.resizeHandle.style.display = 'none';
  }

  toggleMaximize() {
    if (this.container.style.width === '100vw') {
      // 恢复原始大小
      this.container.style.width = `${this.config.size.width}px`;
      this.container.style.height = `${this.config.size.height}px`;
      this.container.style.top = `${this.config.position.y}px`;
      this.container.style.left = `${this.config.position.x}px`;
    } else {
      // 最大化
      this.container.style.width = '100vw';
      this.container.style.height = '100vh';
      this.container.style.top = '0';
      this.container.style.left = '0';
    }

    this.iframe.style.display = 'block';
    this.resizeHandle.style.display = 'block';
  }

  setupPageObservers() {
    // 监听DOM变化，确保悬浮窗不被移除
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
          for (let node of mutation.removedNodes) {
            if (node === this.container) {
              console.log('⚠️ 悬浮窗被移除，重新创建');
              setTimeout(() => this.createFloatingWindow(), 100);
              break;
            }
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.observers.push(observer);
  }

  ensureAlwaysVisible() {
    // 定期检查悬浮窗是否仍然可见
    setInterval(() => {
      if (this.config.enabled && (!this.container || !document.body.contains(this.container))) {
        console.log('⚠️ 悬浮窗丢失，重新创建');
        this.createPersistentFloatingWindow();
      }
    }, 5000);

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      this.adjustPositionOnResize();
    });
  }

  adjustPositionOnResize() {
    if (!this.container) return;

    const maxX = window.innerWidth - this.container.offsetWidth;
    const maxY = window.innerHeight - this.container.offsetHeight;

    const x = Math.max(0, Math.min(this.config.position.x, maxX));
    const y = Math.max(0, Math.min(this.config.position.y, maxY));

    this.container.style.left = `${x}px`;
    this.container.style.top = `${y}px`;

    this.config.position = { x, y };
  }

  handleMessage(request, _sender, _sendResponse) {
    switch (request.type) {
      case 'TOGGLE_FLOATING_WINDOW':
        this.config.enabled = request.enabled;
        if (request.enabled) {
          this.show();
        } else {
          this.hide();
        }
        break;

      case 'TOGGLE_FLOATING_WINDOW_FROM_MENU':
        this.toggle();
        break;

      case 'PLATFORM_BROADCAST':
        this.sendToIframe('PLATFORM_BROADCAST', request.data);
        break;

      case 'PLATFORM_MESSAGE':
        this.sendToIframe('PLATFORM_MESSAGE', request.data);
        break;

      case 'AUTO_CREATE_FLOATING_WINDOW':
        this.handleAutoCreate(request);
        break;

      case 'SYNC_WINDOW_POSITION':
        this.handleSyncPosition(request);
        break;

      case 'SYNC_WINDOW_STATE':
        this.handleSyncState(request);
        break;

      case 'CONFIG_UPDATED':
        this.handleConfigUpdate(request);
        break;
    }
  }

  destroy() {
    // 清理观察者
    this.observers.forEach(observer => observer.disconnect());

    // 移除容器
    if (this.container && document.body.contains(this.container)) {
      document.body.removeChild(this.container);
    }

    // 注销页面实例
    this.unregisterPageInstance();

    console.log('悬浮窗已销毁');
  }

  // 持久化相关方法
  async registerPageInstance() {
    try {
      await chrome.runtime.sendMessage({
        type: 'REGISTER_PAGE_INSTANCE',
        url: window.location.href
      });
    } catch (error) {
      console.error('注册页面实例失败:', error);
    }
  }

  async unregisterPageInstance() {
    try {
      await chrome.runtime.sendMessage({
        type: 'UNREGISTER_PAGE_INSTANCE',
        url: window.location.href
      });
    } catch (error) {
      console.error('注销页面实例失败:', error);
    }
  }

  setupVisibilityMonitor() {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // 页面重新可见，确保悬浮窗存在
        this.ensureFloatingWindowExists();
      }
    });

    // 监听窗口聚焦事件
    window.addEventListener('focus', () => {
      this.ensureFloatingWindowExists();
    });
  }

  async ensureFloatingWindowExists() {
    if (!this.container || !document.body.contains(this.container)) {
      console.log('🔄 页面激活，重新创建悬浮窗');

      // 重新初始化悬浮窗
      await this.initializeWindow();
    }
  }

  handleAutoCreate(request) {
    if (!this.container || !document.body.contains(this.container)) {
      console.log('🔄 自动创建悬浮窗');

      // 更新配置
      this.config = { ...this.config, ...request.config };

      // 使用普通悬浮窗
      this.createFloatingWindow();
    }
  }

  handleSyncPosition(request) {
    if (this.container && request.position) {
      // 普通悬浮窗位置同步
      this.container.style.left = `${request.position.x}px`;
      this.container.style.top = `${request.position.y}px`;

      // 更新配置但不保存状态（避免循环同步）
      this.config.position = request.position;
    }
  }

  handleSyncState(request) {
    if (this.container && request.state) {
      // 普通悬浮窗状态同步
      const { position, size, transparency, visible } = request.state;

      if (position) {
        this.container.style.left = `${position.x}px`;
        this.container.style.top = `${position.y}px`;
        this.config.position = position;
      }

      if (size) {
        this.container.style.width = `${size.width}px`;
        this.container.style.height = `${size.height}px`;
        this.config.size = size;
      }

      if (transparency !== undefined) {
        this.container.style.opacity = transparency;
        this.config.transparency = transparency;
      }

      if (visible !== undefined) {
        if (visible) {
          this.show();
        } else {
          this.hide();
        }
      }
    }
  }

  handleConfigUpdate(request) {
    if (request.config) {
      // 更新配置
      this.config = { ...this.config, ...request.config };
      this.persistentConfig = { ...this.persistentConfig, ...request.config };

      // 应用新配置
      this.applyConfigChanges();
    }
  }

  applyConfigChanges() {
    if (!this.container) return;

    // 应用新的配置到悬浮窗
    this.container.style.opacity = this.config.transparency;

    // 如果启用状态改变
    if (this.config.enabled !== this.isVisible) {
      if (this.config.enabled) {
        this.show();
      } else {
        this.hide();
      }
    }
  }

  async saveWindowState() {
    if (!this.instanceId) return;

    try {
      let state = {};

      if (this.container) {
        // 从普通悬浮窗获取状态
        state = {
          position: this.config.position,
          size: this.config.size,
          transparency: this.config.transparency,
          visible: this.isVisible,
          minimized: this.container.style.height === '30px'
        };
      }

      await chrome.runtime.sendMessage({
        type: 'FLOATING_WINDOW_STATE_CHANGED',
        state: state
      });
    } catch (error) {
      console.error('保存窗口状态失败:', error);
    }
  }


}

// 创建悬浮窗管理器实例
let floatingWindowManager = null;

// 确保只创建一个实例
if (!window.usecasePlatformFloatingWindow) {
  window.usecasePlatformFloatingWindow = true;
  floatingWindowManager = new FloatingWindowManager();
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  if (floatingWindowManager) {
    floatingWindowManager.destroy();
  }
});
