// Element Plus 绿色美拉德主题覆盖
@use 'theme' as theme;

// 覆盖 Element Plus 的主题变量
:root {
  // 主色调
  --el-color-primary: #{theme.$color-primary};
  --el-color-primary-light-3: #{theme.$color-primary-light};
  --el-color-primary-light-5: #{lighten(theme.$color-primary, 15%)};
  --el-color-primary-light-7: #{lighten(theme.$color-primary, 25%)};
  --el-color-primary-light-8: #{lighten(theme.$color-primary, 30%)};
  --el-color-primary-light-9: #{lighten(theme.$color-primary, 35%)};
  --el-color-primary-dark-2: #{theme.$color-primary-dark};

  // 成功色 - 使用绿色系
  --el-color-success: #{theme.$color-primary-accent};
  --el-color-success-light-3: #{lighten(theme.$color-primary-accent, 10%)};
  --el-color-success-light-5: #{lighten(theme.$color-primary-accent, 20%)};
  --el-color-success-light-7: #{lighten(theme.$color-primary-accent, 30%)};
  --el-color-success-light-8: #{lighten(theme.$color-primary-accent, 35%)};
  --el-color-success-light-9: #{lighten(theme.$color-primary-accent, 40%)};
  --el-color-success-dark-2: #{darken(theme.$color-primary-accent, 10%)};

  // 信息色
  --el-color-info: #{theme.$color-secondary};
  --el-color-info-light-3: #{lighten(theme.$color-secondary, 10%)};
  --el-color-info-light-5: #{lighten(theme.$color-secondary, 20%)};
  --el-color-info-light-7: #{lighten(theme.$color-secondary, 30%)};
  --el-color-info-light-8: #{lighten(theme.$color-secondary, 35%)};
  --el-color-info-light-9: #{lighten(theme.$color-secondary, 40%)};
  --el-color-info-dark-2: #{darken(theme.$color-secondary, 10%)};

  // 警告色 - 使用温暖的橙绿色
  --el-color-warning: #d4a574;
  --el-color-warning-light-3: #ddb485;
  --el-color-warning-light-5: #e6c396;
  --el-color-warning-light-7: #efd2a7;
  --el-color-warning-light-8: #f2d8b2;
  --el-color-warning-light-9: #f5debd;
  --el-color-warning-dark-2: #c19663;

  // 危险色 - 使用柔和的红棕色
  --el-color-danger: #b58a8a;
  --el-color-danger-light-3: #c19b9b;
  --el-color-danger-light-5: #cdacac;
  --el-color-danger-light-7: #d9bdbd;
  --el-color-danger-light-8: #dcc8c8;
  --el-color-danger-light-9: #dfd3d3;
  --el-color-danger-dark-2: #a47979;

  // 错误色
  --el-color-error: var(--el-color-danger);

  // 文本颜色
  --el-text-color-primary: #{theme.$color-text-primary};
  --el-text-color-regular: #{theme.$color-text-secondary};
  --el-text-color-secondary: #{theme.$color-text-tertiary};
  --el-text-color-placeholder: #{theme.$color-text-disabled};
  --el-text-color-disabled: #{theme.$color-text-disabled};

  // 边框颜色
  --el-border-color: #{theme.$color-border};
  --el-border-color-light: #{lighten(theme.$color-border, 5%)};
  --el-border-color-lighter: #{lighten(theme.$color-border, 10%)};
  --el-border-color-extra-light: #{lighten(theme.$color-border, 15%)};
  --el-border-color-dark: #{darken(theme.$color-border, 5%)};
  --el-border-color-darker: #{darken(theme.$color-border, 10%)};

  // 填充颜色
  --el-fill-color: #{theme.$color-container};
  --el-fill-color-light: #{lighten(theme.$color-container, 2%)};
  --el-fill-color-lighter: #{lighten(theme.$color-container, 4%)};
  --el-fill-color-extra-light: #{lighten(theme.$color-container, 6%)};
  --el-fill-color-dark: #{darken(theme.$color-container, 2%)};
  --el-fill-color-darker: #{darken(theme.$color-container, 4%)};
  --el-fill-color-blank: #{theme.$color-background};

  // 背景颜色
  --el-bg-color: #{theme.$color-background};
  --el-bg-color-page: #{theme.$color-background};
  --el-bg-color-overlay: #{theme.$color-container};

  // 边框半径
  --el-border-radius-base: #{theme.$border-radius-medium};
  --el-border-radius-small: #{theme.$border-radius-small};
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;

  // 字体
  --el-font-family: #{theme.$font-family-primary};

  // 阴影
  --el-box-shadow: #{theme.$shadow-soft};
  --el-box-shadow-light: #{theme.$shadow-soft};
  --el-box-shadow-base: #{theme.$shadow-medium};
  --el-box-shadow-dark: #{theme.$shadow-strong};
}

// 自定义组件样式
.el-button {
  &--primary {
    background: linear-gradient(
      135deg,
      #{theme.$color-primary} 0%,
      #{theme.$color-primary-dark} 100%
    );
    border-color: #{theme.$color-primary};

    &:hover {
      background: linear-gradient(
        135deg,
        #{theme.$color-primary-light} 0%,
        #{theme.$color-primary} 100%
      );
      border-color: #{theme.$color-primary-light};
      transform: translateY(-1px);
      box-shadow: #{theme.$shadow-glow-primary};
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.el-input {
  .el-input__wrapper {
    background-color: #{lighten(theme.$color-container, 2%)};
    border-color: #{theme.$color-border};
    border-radius: #{theme.$border-radius-medium};

    &:hover {
      border-color: #{theme.$color-border-focus};
    }

    &.is-focus {
      border-color: #{theme.$color-primary-accent};
      box-shadow: 0 0 0 2px #{rgba(theme.$color-primary-accent, 0.15)};
    }
  }
}

.el-card {
  background-color: #{theme.$color-container};
  border-color: #{theme.$color-border};
  border-radius: #{theme.$border-radius-large};
  box-shadow: #{theme.$shadow-soft};

  .el-card__header {
    background: linear-gradient(
      135deg,
      #{lighten(theme.$color-container, 2%)} 0%,
      #{theme.$color-container} 100%
    );
    border-bottom-color: #{theme.$color-border};
  }
}

.el-menu {
  &--horizontal {
    border-bottom-color: #{theme.$color-border};
  }

  .el-menu-item {
    &:hover {
      background-color: #{rgba(theme.$color-primary, 0.1)} !important;
      color: #{theme.$color-primary} !important;
    }

    &.is-active {
      background-color: #{rgba(theme.$color-primary, 0.15)} !important;
      color: #{theme.$color-primary} !important;
      border-bottom-color: #{theme.$color-primary} !important;
    }
  }
}

.el-table {
  background-color: #{theme.$color-container};
  color: #{theme.$color-text-primary};

  th.el-table__cell {
    background-color: #{lighten(theme.$color-container, 3%)};
    border-bottom-color: #{theme.$color-border};
    color: #{theme.$color-text-primary};
  }

  td.el-table__cell {
    border-bottom-color: #{lighten(theme.$color-border, 5%)};
  }

  .el-table__row {
    &:hover {
      background-color: #{rgba(theme.$color-primary, 0.05)} !important;
    }
  }
}

.el-pagination {
  .el-pager li {
    &:hover {
      color: #{theme.$color-primary};
    }

    &.is-active {
      background-color: #{theme.$color-primary};
      color: #{theme.$color-text-light};
    }
  }

  .btn-prev,
  .btn-next {
    &:hover {
      color: #{theme.$color-primary};
    }
  }
}

.el-dialog {
  background-color: #{theme.$color-container};
  border-radius: #{theme.$border-radius-large};
  box-shadow: #{theme.$shadow-strong};

  .el-dialog__header {
    background: linear-gradient(
      135deg,
      #{lighten(theme.$color-container, 2%)} 0%,
      #{theme.$color-container} 100%
    );
    border-bottom: 1px solid #{theme.$color-border};
    border-radius: #{theme.$border-radius-large} #{theme.$border-radius-large} 0 0;
  }
}

.el-message {
  background-color: #{theme.$color-container};
  border-color: #{theme.$color-border};
  border-radius: #{theme.$border-radius-medium};
  box-shadow: #{theme.$shadow-medium};

  &.el-message--success {
    background-color: #{rgba(theme.$color-primary-accent, 0.1)};
    border-color: #{theme.$color-primary-accent};

    .el-message__content {
      color: #{darken(theme.$color-primary-accent, 20%)};
    }
  }
}

.el-notification {
  background-color: #{theme.$color-container};
  border-color: #{theme.$color-border};
  border-radius: #{theme.$border-radius-large};
  box-shadow: #{theme.$shadow-strong};
}
