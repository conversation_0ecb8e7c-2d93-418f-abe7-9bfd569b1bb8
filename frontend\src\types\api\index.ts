/**
 * API类型定义统一导出文件
 * 提供版本管理和向后兼容性支持
 */

// 版本信息
export const API_TYPES_VERSION = '1.0.0'
export const LAST_UPDATED = '2025-07-29'

// 通用类型
export * from './common'

// 用户管理
export * from './user'

// 会话管理
export * from './session'

// 浏览器管理
export * from './browser'

// 用例管理
export * from './case-management'

// 录制管理
export * from './recording'

// 报告管理
export * from './reports'

// 提示词管理
export * from './prompts'

/**
 * API版本兼容性映射
 * 用于处理不同版本API的兼容性问题
 */
export interface ApiVersionMapping {
  v1: {
    // v1版本的接口映射
    browser: typeof import('./browser')
    caseManagement: typeof import('./case-management')
    recording: typeof import('./recording')
    reports: typeof import('./reports')
    prompts: typeof import('./prompts')
  }
}

/**
 * 接口变更记录
 * 用于跟踪接口的变更历史
 */
export interface ApiChangeLog {
  version: string
  date: string
  changes: {
    added?: string[]
    modified?: string[]
    deprecated?: string[]
    removed?: string[]
  }
  breaking_changes?: string[]
  migration_guide?: string
}

/**
 * API类型定义变更历史
 */
export const API_CHANGE_HISTORY: ApiChangeLog[] = [
  {
    version: '1.0.0',
    date: '2025-07-29',
    changes: {
      added: [
        'browser.ts - 浏览器管理类型定义',
        'case-management.ts - 用例管理类型定义',
        'recording.ts - 录制管理类型定义',
        'reports.ts - 报告管理类型定义',
        'prompts.ts - 提示词管理类型定义',
        '版本管理机制',
        '接口标准化结构'
      ]
    },
    breaking_changes: [],
    migration_guide: '首次发布，无需迁移'
  }
]

/**
 * 类型安全检查工具
 */
export interface TypeSafetyConfig {
  enableRuntimeValidation: boolean
  strictMode: boolean
  deprecationWarnings: boolean
  migrationAssistance: boolean
}

/**
 * 默认类型安全配置
 */
export const DEFAULT_TYPE_SAFETY_CONFIG: TypeSafetyConfig = {
  enableRuntimeValidation: process.env.NODE_ENV === 'development',
  strictMode: true,
  deprecationWarnings: true,
  migrationAssistance: true
}

/**
 * 接口废弃标记
 */
export interface DeprecatedInterface {
  name: string
  version: string
  deprecatedSince: string
  removeInVersion: string
  replacement?: string
  migrationPath?: string
}

/**
 * 当前废弃的接口列表
 */
export const DEPRECATED_INTERFACES: DeprecatedInterface[] = [
  // 目前没有废弃的接口
]

/**
 * 接口兼容性检查函数
 */
export function checkApiCompatibility(requiredVersion: string): boolean {
  // 简单的版本比较逻辑
  const current = API_TYPES_VERSION.split('.').map(Number)
  const required = requiredVersion.split('.').map(Number)
  
  for (let i = 0; i < Math.max(current.length, required.length); i++) {
    const currentPart = current[i] || 0
    const requiredPart = required[i] || 0
    
    if (currentPart > requiredPart) return true
    if (currentPart < requiredPart) return false
  }
  
  return true
}

/**
 * 获取接口变更信息
 */
export function getChangesSince(version: string): ApiChangeLog[] {
  const targetIndex = API_CHANGE_HISTORY.findIndex(log => log.version === version)
  if (targetIndex === -1) return API_CHANGE_HISTORY
  
  return API_CHANGE_HISTORY.slice(0, targetIndex)
}

/**
 * 检查是否有破坏性变更
 */
export function hasBreakingChanges(fromVersion: string, toVersion: string): boolean {
  const changes = getChangesSince(fromVersion)
  return changes.some(change => 
    change.breaking_changes && change.breaking_changes.length > 0
  )
}

/**
 * 类型定义元数据
 */
export interface TypeMetadata {
  name: string
  module: string
  version: string
  description?: string
  examples?: any[]
  relatedTypes?: string[]
  validationRules?: Record<string, any>
}

/**
 * 导出所有类型的元数据映射
 */
export const TYPE_METADATA_MAP: Record<string, TypeMetadata> = {
  // 这里可以添加具体的类型元数据
  // 用于自动生成文档、验证等
}
