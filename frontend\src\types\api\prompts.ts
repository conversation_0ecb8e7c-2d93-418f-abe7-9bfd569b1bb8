/**
 * 提示词管理相关API类型定义
 * 标准化提示词创建、管理、执行等接口
 */

import type { ApiResponse, BaseResponse } from './common'

/**
 * 提示词项目接口
 */
export interface PromptItem {
  prompt_id: string
  title: string
  content: string
  description?: string
  category?: string
  status?: string
  tags?: string[]
  variables?: string[]
  created_by?: string
  created_at?: string
  updated_at?: string
  usage_count?: number
  is_public?: boolean
}

/**
 * 提示词创建请求接口
 */
export interface PromptCreateRequest {
  title: string
  content: string
  description?: string
  category?: string
  tags?: string[]
  variables?: string[]
  is_public?: boolean
}

/**
 * 提示词更新请求接口
 */
export interface PromptUpdateRequest extends Partial<PromptCreateRequest> {}

/**
 * 提示词列表响应数据
 */
export interface PromptListResponseData {
  prompts: PromptItem[]
  total: number
  page: number
  page_size: number
}

/**
 * 提示词列表响应接口
 */
export interface PromptListResponse extends ApiResponse<PromptListResponseData> {}

/**
 * 提示词详情响应接口
 */
export interface PromptDetailResponse extends ApiResponse<PromptItem> {}

/**
 * 提示词创建响应接口
 */
export interface PromptCreateResponse extends ApiResponse<PromptItem> {}

/**
 * 提示词删除响应接口
 */
export interface PromptDeleteResponse extends BaseResponse<{
  prompt_id: string
  deleted_at: string
}> {}

/**
 * 提示词执行请求接口
 */
export interface PromptExecuteRequest {
  variables?: Record<string, any>
  context?: string
  options?: {
    temperature?: number
    max_tokens?: number
    stream?: boolean
  }
}

/**
 * 提示词执行响应数据
 */
export interface PromptExecuteResponseData {
  result: string
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  model: string
  created_at: string
  execution_time: number
}

/**
 * 提示词执行响应接口
 */
export interface PromptExecuteResponse extends ApiResponse<PromptExecuteResponseData> {}

/**
 * 提示词分类接口
 */
export interface PromptCategory {
  id: string
  name: string
  description?: string
  prompt_count: number
  created_at: string
}

/**
 * 提示词分类列表响应
 */
export interface PromptCategoryListResponse extends ApiResponse<PromptCategory[]> {}

/**
 * 提示词搜索请求接口
 */
export interface PromptSearchRequest {
  query?: string
  category?: string
  tags?: string[]
  status?: string
  created_by?: string
  page?: number
  page_size?: number
  sort_by?: 'created_at' | 'updated_at' | 'usage_count' | 'title'
  sort_order?: 'asc' | 'desc'
}

/**
 * 提示词状态枚举
 */
export enum PromptStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DEPRECATED = 'deprecated'
}

/**
 * 提示词变量类型枚举
 */
export enum PromptVariableType {
  TEXT = 'text',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  SELECT = 'select',
  MULTISELECT = 'multiselect'
}

/**
 * 提示词变量定义接口
 */
export interface PromptVariable {
  name: string
  type: PromptVariableType
  description?: string
  required?: boolean
  default_value?: any
  options?: string[]  // 用于 select 和 multiselect 类型
  validation?: {
    min?: number
    max?: number
    pattern?: string
  }
}

/**
 * 提示词模板接口
 */
export interface PromptTemplate {
  template_id: string
  name: string
  description?: string
  content: string
  variables: PromptVariable[]
  category: string
  is_system: boolean
  created_at: string
  updated_at: string
}

/**
 * 提示词使用统计接口
 */
export interface PromptUsageStats {
  prompt_id: string
  total_executions: number
  successful_executions: number
  failed_executions: number
  average_execution_time: number
  last_used_at: string
  most_used_variables: string[]
}
