// 影子DOM管理器
export class ShadowDOMManager {
  private shadowRoots = new Map<string, ShadowRoot>()
  private containers = new Map<string, HTMLElement>()

  /**
   * 为用户创建影子DOM根节点
   */
  createShadowRoot(userId: string): ShadowRoot {
    // 检查是否已存在
    if (this.shadowRoots.has(userId)) {
      return this.shadowRoots.get(userId)!
    }

    // 创建容器元素
    const container = document.createElement('div')
    container.id = `user-container-${userId}`
    container.className = 'user-shadow-container'
    container.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0;
      transition: opacity 0.3s ease;
    `

    // 将容器添加到body
    document.body.appendChild(container)

    // 创建影子DOM
    const shadowRoot = container.attachShadow({ mode: 'closed' })

    // 添加基础样式
    this.addBaseShadowStyles(shadowRoot)

    // 创建应用根容器
    const appRoot = document.createElement('div')
    appRoot.id = 'app-root'
    appRoot.className = 'shadow-app-root'
    shadowRoot.appendChild(appRoot)

    // 存储引用
    this.shadowRoots.set(userId, shadowRoot)
    this.containers.set(userId, container)

    return shadowRoot
  }

  /**
   * 添加基础影子DOM样式
   */
  private addBaseShadowStyles(shadowRoot: ShadowRoot): void {
    const style = document.createElement('style')
    style.textContent = `
      /* 重置样式 */
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      /* 应用根容器 */
      .shadow-app-root {
        width: 100%;
        height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background: #fff;
        overflow: hidden;
      }

      /* 隔离样式 */
      .shadow-app-root * {
        isolation: isolate;
      }

      /* 防止样式污染 */
      .shadow-app-root input,
      .shadow-app-root button,
      .shadow-app-root select,
      .shadow-app-root textarea {
        font-family: inherit;
        font-size: inherit;
      }

      /* 滚动条样式 */
      .shadow-app-root ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      .shadow-app-root ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      .shadow-app-root ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      .shadow-app-root ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      /* 动画 */
      .shadow-app-root .fade-enter-active,
      .shadow-app-root .fade-leave-active {
        transition: opacity 0.3s ease;
      }

      .shadow-app-root .fade-enter-from,
      .shadow-app-root .fade-leave-to {
        opacity: 0;
      }

      /* 响应式断点 */
      @media (max-width: 768px) {
        .shadow-app-root {
          font-size: 13px;
        }
      }

      @media (max-width: 480px) {
        .shadow-app-root {
          font-size: 12px;
        }
      }
    `
    shadowRoot.appendChild(style)
  }

  /**
   * 获取用户的影子DOM根节点
   */
  getShadowRoot(userId: string): ShadowRoot | null {
    return this.shadowRoots.get(userId) || null
  }

  /**
   * 获取用户的容器元素
   */
  getContainer(userId: string): HTMLElement | null {
    return this.containers.get(userId) || null
  }

  /**
   * 激活用户的影子DOM（显示）
   */
  activateShadowRoot(userId: string): boolean {
    const container = this.containers.get(userId)
    if (!container) {
      return false
    }

    // 隐藏其他用户的容器
    this.containers.forEach((otherContainer, otherUserId) => {
      if (otherUserId !== userId) {
        otherContainer.style.pointerEvents = 'none'
        otherContainer.style.zIndex = '-1'
        otherContainer.style.opacity = '0'
      }
    })

    // 显示当前用户的容器
    container.style.pointerEvents = 'auto'
    container.style.zIndex = '1000'
    container.style.opacity = '1'

    return true
  }

  /**
   * 停用用户的影子DOM（隐藏）
   */
  deactivateShadowRoot(userId: string): boolean {
    const container = this.containers.get(userId)
    if (!container) {
      return false
    }

    container.style.pointerEvents = 'none'
    container.style.zIndex = '-1'
    container.style.opacity = '0'

    return true
  }

  /**
   * 向影子DOM中添加样式
   */
  addStyleToShadowRoot(userId: string, css: string): boolean {
    const shadowRoot = this.shadowRoots.get(userId)
    if (!shadowRoot) {
      return false
    }

    const style = document.createElement('style')
    style.textContent = css
    shadowRoot.appendChild(style)

    return true
  }

  /**
   * 向影子DOM中添加外部样式表
   */
  addLinkToShadowRoot(userId: string, href: string): boolean {
    const shadowRoot = this.shadowRoots.get(userId)
    if (!shadowRoot) {
      return false
    }

    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = href
    shadowRoot.appendChild(link)

    return true
  }

  /**
   * 获取影子DOM中的应用根容器
   */
  getAppRoot(userId: string): HTMLElement | null {
    const shadowRoot = this.shadowRoots.get(userId)
    if (!shadowRoot) return null

    return shadowRoot.querySelector('#app-root') as HTMLElement
  }

  /**
   * 在影子DOM中执行脚本
   */
  executeScriptInShadowRoot(userId: string, script: string): boolean {
    const shadowRoot = this.shadowRoots.get(userId)
    if (!shadowRoot) {
      return false
    }

    try {
      // 创建脚本元素
      const scriptElement = document.createElement('script')
      scriptElement.textContent = script
      shadowRoot.appendChild(scriptElement)

      return true
    } catch (error) {
      console.warn('Shadow DOM cleanup failed:', error)
      return false
    }
  }

  /**
   * 移除用户的影子DOM
   */
  removeShadowRoot(userId: string): boolean {
    const container = this.containers.get(userId)
    const shadowRoot = this.shadowRoots.get(userId)

    if (container) {
      // 从DOM中移除容器
      container.remove()
      this.containers.delete(userId)
    }

    if (shadowRoot) {
      this.shadowRoots.delete(userId)
    }

    if (container || shadowRoot) {
      return true
    }

    return false
  }

  /**
   * 清理所有影子DOM
   */
  clearAllShadowRoots(): void {
    // 移除所有容器
    this.containers.forEach((container) => {
      container.remove()
    })

    // 清空映射
    this.containers.clear()
    this.shadowRoots.clear()
  }

  // 删除未使用的getStats方法

  /**
   * 切换影子DOM可见性
   */
  toggleShadowRootVisibility(userId: string): boolean {
    const container = this.containers.get(userId)
    if (!container) {
      return false
    }

    const isVisible = container.style.opacity === '1'

    if (isVisible) {
      this.deactivateShadowRoot(userId)
    } else {
      this.activateShadowRoot(userId)
    }

    return !isVisible
  }

  /**
   * 检查影子DOM是否支持
   */
  static isSupported(): boolean {
    return 'attachShadow' in Element.prototype
  }

  /**
   * 获取当前活跃的影子DOM用户
   */
  getActiveUser(): string | null {
    for (const [userId, container] of this.containers) {
      if (container.style.opacity === '1' && container.style.zIndex === '1000') {
        return userId
      }
    }
    return null
  }

  /**
   * 克隆主文档的样式到影子DOM
   */
  cloneMainDocumentStyles(userId: string): boolean {
    const shadowRoot = this.shadowRoots.get(userId)
    if (!shadowRoot) {
      return false
    }

    // 克隆所有link标签
    const links = document.querySelectorAll('link[rel="stylesheet"]')
    links.forEach((link) => {
      const clonedLink = link.cloneNode(true) as HTMLLinkElement
      shadowRoot.appendChild(clonedLink)
    })

    // 克隆所有style标签
    const styles = document.querySelectorAll('style')
    styles.forEach((style) => {
      const clonedStyle = style.cloneNode(true) as HTMLStyleElement
      shadowRoot.appendChild(clonedStyle)
    })

    return true
  }
}
