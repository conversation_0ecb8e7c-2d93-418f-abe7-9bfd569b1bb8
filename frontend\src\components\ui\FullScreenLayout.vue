<template>
  <div class="fullscreen-layout-container">
    <!-- 顶部工具栏区域 -->
    <div v-if="$slots.toolbar || showDefaultToolbar" class="toolbar-section">
      <slot name="toolbar">
        <div class="default-toolbar">
          <div class="toolbar-left">
            <h2 class="page-title">{{ title }}</h2>
          </div>
          <div class="toolbar-right">
            <slot name="toolbar-actions"></slot>
          </div>
        </div>
      </slot>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-section">
      <slot name="content">
        <!-- 默认三列布局 -->
        <div class="content-panel primary-panel">
          <div class="panel-header">
            <h3>{{ primaryTitle || '主要内容' }}</h3>
            <div class="panel-controls">
              <slot name="primary-controls"></slot>
            </div>
          </div>
          <div class="panel-content">
            <slot name="primary"></slot>
          </div>
        </div>

        <div v-if="$slots.secondary || showSecondary" class="content-panel secondary-panel">
          <div class="panel-header">
            <h3>{{ secondaryTitle || '辅助内容' }}</h3>
            <div class="panel-controls">
              <slot name="secondary-controls"></slot>
            </div>
          </div>
          <div class="panel-content">
            <slot name="secondary"></slot>
          </div>
        </div>

        <div v-if="$slots.sidebar || showSidebar" class="content-panel sidebar-panel">
          <div class="panel-header">
            <h3>{{ sidebarTitle || '侧边信息' }}</h3>
            <div class="panel-controls">
              <slot name="sidebar-controls"></slot>
            </div>
          </div>
          <div class="panel-content">
            <slot name="sidebar"></slot>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, withDefaults } from 'vue'

interface Props {
  title?: string
  primaryTitle?: string
  secondaryTitle?: string
  sidebarTitle?: string
  showDefaultToolbar?: boolean
  showSecondary?: boolean
  showSidebar?: boolean
  layout?: 'single' | 'double' | 'triple'
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  primaryTitle: '',
  secondaryTitle: '',
  sidebarTitle: '',
  showDefaultToolbar: true,
  showSecondary: true,
  showSidebar: true,
  layout: 'triple',
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/theme.scss' as *;

.fullscreen-layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
  padding: 16px;
  background: #f0f2f5;

  .toolbar-section {
    background: #fff;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;

    .default-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .toolbar-right {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }
  }

  .main-content-section {
    flex: 1;
    display: flex;
    gap: 16px;
    min-height: 0;
    width: 100%;
    height: 100%;

    .content-panel {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      min-height: 0;
      height: 100%;

      &.primary-panel {
        flex: 2;
      }

      &.secondary-panel {
        flex: 1.5;
      }

      &.sidebar-panel {
        flex: 1;
        min-width: 280px;
      }

      .panel-header {
        padding: 16px 20px;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .panel-controls {
          display: flex;
          gap: 8px;
          align-items: center;
        }
      }

      .panel-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        min-height: 0;
        height: 100%;
      }
    }
  }
}

// 根据layout属性调整布局
.fullscreen-layout-container[data-layout='single'] {
  .main-content-section {
    .secondary-panel,
    .sidebar-panel {
      display: none;
    }

    .primary-panel {
      flex: 1;
    }
  }
}

.fullscreen-layout-container[data-layout='double'] {
  .main-content-section {
    .sidebar-panel {
      display: none;
    }

    .primary-panel {
      flex: 2;
    }

    .secondary-panel {
      flex: 1;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content-section {
    flex-direction: column;

    .content-panel {
      &.primary-panel,
      &.secondary-panel,
      &.sidebar-panel {
        flex: none;
        height: 400px;
      }
    }
  }
}

@media (max-width: 768px) {
  .fullscreen-layout-container {
    gap: 12px;
  }

  .toolbar-section {
    padding: 12px 16px;
  }

  .content-panel {
    .panel-header {
      padding: 12px 16px;
    }

    .panel-content {
      padding: 16px;
    }
  }
}
</style>
