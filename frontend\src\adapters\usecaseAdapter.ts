import { apiService } from '../services/api'

// 树形节点接口
export interface TreeNode {
  id: string | number
  label: string
  children?: TreeNode[]
  type: 'system' | 'function' | 'testcase' | 'usecase'
  level: number
  showActions?: boolean
  [key: string]: any
}

// 用例内容接口
export interface UsecaseContent {
  id: string
  title: string
  content: string
  version: string
  versions: string[]
  screenshots: Screenshot[]
  createdAt: string
  updatedAt: string
}

// 截图接口
export interface Screenshot {
  id: string | number
  url: string
  description: string
  createdAt?: string
}

// 启动配置接口
export interface LaunchConfig {
  method: string
  url: string
  sessionId?: string
}

// 用例数据适配器类
class UsecaseAdapter {
  /**
   * 将现有会话数据转换为用例树形结构
   */
  convertSessionsToTree(sessions: any[]): TreeNode[] {
    if (!sessions || sessions.length === 0) {
      return this.getDefaultTreeData()
    }

    const treeMap = new Map<string, TreeNode>()

    // 创建系统根节点
    const systemNode: TreeNode = {
      id: 'system_1',
      label: '用例管理系统',
      type: 'system',
      level: 0,
      children: [],
    }

    sessions.forEach((session, index) => {
      // 从会话名称推断功能分类
      const functionName = this.extractFunctionName(session.name)
      const functionId = `function_${functionName}`

      if (!treeMap.has(functionId)) {
        const functionNode: TreeNode = {
          id: functionId,
          label: functionName,
          type: 'function',
          level: 1,
          children: [],
        }
        treeMap.set(functionId, functionNode)
        systemNode.children!.push(functionNode)
      }

      // 创建测试项节点
      const testcaseNode: TreeNode = {
        id: `testcase_${session.id}`,
        label: session.name || `测试项${index + 1}`,
        type: 'testcase',
        level: 2,
        children: [],
      }

      // 从录制步骤创建用例节点
      if (session.steps && session.steps.length > 0) {
        session.steps.forEach((step: any, stepIndex: number) => {
          const usecaseNode: TreeNode = {
            id: `usecase_${session.id}_${stepIndex}`,
            label: step.description || `用例${stepIndex + 1}`,
            type: 'usecase',
            level: 3,
            sessionId: session.id,
            stepIndex: stepIndex,
          }
          testcaseNode.children!.push(usecaseNode)
        })
      } else {
        // 如果没有步骤，创建默认用例
        const usecaseNode: TreeNode = {
          id: `usecase_${session.id}_0`,
          label: '默认用例',
          type: 'usecase',
          level: 3,
          sessionId: session.id,
        }
        testcaseNode.children!.push(usecaseNode)
      }

      treeMap.get(functionId)!.children!.push(testcaseNode)
    })

    return [systemNode]
  }

  /**
   * 从会话名称提取功能名称
   */
  private extractFunctionName(sessionName: string): string {
    if (!sessionName) return '默认功能'

    // 简单的功能名称提取逻辑，可以根据实际需求优化
    const functionMap: Record<string, string> = {
      login: '用户登录',
      search: '搜索功能',
      form: '表单操作',
      navigation: '页面导航',
      upload: '文件上传',
      download: '文件下载',
    }

    for (const [keyword, functionName] of Object.entries(functionMap)) {
      if (sessionName.toLowerCase().includes(keyword)) {
        return functionName
      }
    }

    return '通用功能'
  }

  /**
   * 获取默认树形数据（当没有会话数据时）
   */
  getDefaultTreeData(): TreeNode[] {
    return [
      {
        id: 1,
        label: '用例管理系统',
        type: 'system',
        level: 0,
        children: [
          {
            id: 2,
            label: '用户管理功能',
            type: 'function',
            level: 1,
            children: [
              {
                id: 3,
                label: '用户登录测试',
                type: 'testcase',
                level: 2,
                children: [
                  { id: 4, label: '正常登录用例', type: 'usecase', level: 3 },
                  { id: 5, label: '错误密码用例', type: 'usecase', level: 3 },
                ],
              },
            ],
          },
        ],
      },
    ]
  }

  /**
   * 启动浏览器会话
   */
  async launchBrowser(
    config: LaunchConfig,
  ): Promise<{ success: boolean; message: string; sessionId?: string }> {
    try {
      // 如果没有现有会话，创建新会话
      let sessionId = config.sessionId
      if (!sessionId) {
        const sessionData = {
          name: `Browser_${Date.now()}`,
          description: `启动浏览器访问: ${config.url}`,
          config: {
            launch_method: config.method,
            target_url: config.url,
          },
        }

        const session = await apiService.sessions.create(sessionData)
        sessionId = session.data.id
      }

      // 启动浏览器
      // const launchData = {
      //   url: config.url,
      //   method: config.method,
      // }

      // 注意：这里需要根据实际API调整
      // await apiService.sessions.launch(sessionId, launchData)

      return {
        success: true,
        message: '浏览器启动成功',
        sessionId: sessionId,
      }
    } catch (error) {
      return {
        success: false,
        message: '浏览器启动失败: ' + (error as Error).message,
      }
    }
  }

  /**
   * 将会话步骤转换为用例内容
   */
  convertSessionStepsToContent(session: any, stepIndex?: number): UsecaseContent {
    if (!session) {
      return this.getDefaultUsecaseContent()
    }

    let content = ''
    let screenshots: Screenshot[] = []

    if (stepIndex !== undefined && session.steps?.[stepIndex]) {
      const step = session.steps[stepIndex]
      content = this.formatStepContent(step)

      // 如果有截图，转换格式
      if (step.screenshot) {
        screenshots.push({
          id: `screenshot_${step.timestamp}`,
          url: step.screenshot,
          description: step.description || '步骤截图',
        })
      }
    } else {
      // 转换所有步骤
      content =
        session.steps
          ?.map((step: any, index: number) => `步骤${index + 1}: ${this.formatStepContent(step)}`)
          .join('\n\n') || '暂无内容'

      // 收集所有截图
      screenshots =
        session.steps
          ?.filter((step: any) => step.screenshot)
          .map((step: any, index: number) => ({
            id: `screenshot_${index}`,
            url: step.screenshot,
            description: step.description || `步骤${index + 1}截图`,
          })) || []
    }

    return {
      id: session.id,
      title: session.name || '未命名用例',
      content: content,
      version: 'v1.0',
      versions: ['v1.0'],
      screenshots: screenshots,
      createdAt: session.created_at || new Date().toISOString(),
      updatedAt: session.updated_at || new Date().toISOString(),
    }
  }

  /**
   * 格式化步骤内容
   */
  private formatStepContent(step: any): string {
    const parts = []

    if (step.action) {
      parts.push(`操作: ${step.action}`)
    }

    if (step.selector) {
      parts.push(`选择器: ${step.selector}`)
    }

    if (step.value) {
      parts.push(`值: ${step.value}`)
    }

    if (step.description) {
      parts.push(`描述: ${step.description}`)
    }

    return parts.join('\n') || '无详细信息'
  }

  /**
   * 获取默认用例内容
   */
  getDefaultUsecaseContent(): UsecaseContent {
    return {
      id: 'default',
      title: '示例用例',
      content:
        '这是一个示例用例内容。\n\n请在此处编写测试步骤：\n1. 打开应用\n2. 执行操作\n3. 验证结果',
      version: 'v1.0',
      versions: ['v1.0', 'v1.1', 'v2.0'],
      screenshots: [
        {
          id: 'demo1',
          url: '/api/placeholder/300/200',
          description: '示例截图1',
        },
        {
          id: 'demo2',
          url: '/api/placeholder/300/200',
          description: '示例截图2',
        },
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  }

  /**
   * 保存用例内容
   */
  async saveUsecaseContent(
    usecaseId: string,
    content: UsecaseContent,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // 这里可以调用实际的保存API
      // 目前使用本地存储模拟
      const key = `usecase_${usecaseId}`
      localStorage.setItem(key, JSON.stringify(content))

      return {
        success: true,
        message: '用例内容保存成功',
      }
    } catch (error) {
      return {
        success: false,
        message: '保存失败: ' + (error as Error).message,
      }
    }
  }

  /**
   * 加载用例内容
   */
  async loadUsecaseContent(usecaseId: string): Promise<UsecaseContent | null> {
    try {
      const key = `usecase_${usecaseId}`
      const stored = localStorage.getItem(key)

      if (stored) {
        return JSON.parse(stored)
      }

      return null
    } catch (_error) {
      return null
    }
  }
}

// 导出适配器实例
export const usecaseAdapter = new UsecaseAdapter()
export default usecaseAdapter
