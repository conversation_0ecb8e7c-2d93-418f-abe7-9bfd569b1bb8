<template>
  <FullScreenLayout
    title="用例统计"
    primary-title="用例执行统计"
    secondary-title="质量分析"
    sidebar-title="筛选和操作"
    layout="triple"
  >
    <template #toolbar-actions>
      <el-button type="primary" @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
      <el-button @click="exportReport">
        <el-icon><Download /></el-icon>
        导出报告
      </el-button>
      <el-button @click="openSettings">
        <el-icon><Setting /></el-icon>
        设置
      </el-button>
    </template>

    <template #primary>
      <div class="usecase-charts">
        <!-- 执行状态分布 -->
        <div class="chart-section">
          <h4>用例执行状态分布</h4>
          <div class="chart-placeholder">
            <el-icon><PieChart /></el-icon>
            <p>执行状态饼图</p>
          </div>
        </div>

        <!-- 执行趋势 -->
        <div class="chart-section">
          <h4>执行趋势分析</h4>
          <div class="chart-placeholder">
            <el-icon><TrendCharts /></el-icon>
            <p>趋势线图</p>
          </div>
        </div>

        <!-- 缺陷统计 -->
        <div class="chart-section">
          <h4>缺陷统计</h4>
          <div class="chart-placeholder">
            <el-icon><Histogram /></el-icon>
            <p>缺陷柱状图</p>
          </div>
        </div>
      </div>
    </template>

    <template #secondary>
      <div class="quality-analysis">
        <!-- 关键指标 -->
        <div class="kpi-section">
          <div class="kpi-grid">
            <div class="kpi-card">
              <div class="kpi-value">{{ statistics.totalUsecases }}</div>
              <div class="kpi-label">总用例数</div>
            </div>
            <div class="kpi-card">
              <div class="kpi-value">{{ statistics.passedUsecases }}</div>
              <div class="kpi-label">通过用例</div>
            </div>
            <div class="kpi-card">
              <div class="kpi-value">{{ statistics.failedUsecases }}</div>
              <div class="kpi-label">失败用例</div>
            </div>
            <div class="kpi-card">
              <div class="kpi-value">{{ statistics.passRate }}%</div>
              <div class="kpi-label">通过率</div>
            </div>
          </div>
        </div>

        <!-- 质量趋势 -->
        <div class="trend-section">
          <h4>质量趋势</h4>
          <div class="trend-chart">
            <el-icon><TrendCharts /></el-icon>
            <p>质量趋势图</p>
          </div>
        </div>

        <!-- 用例分类统计 -->
        <div class="category-stats">
          <h4>用例分类统计</h4>
          <el-table :data="categoryData" size="small" height="200">
            <el-table-column prop="category" label="分类" width="80" />
            <el-table-column prop="total" label="总数" width="60" />
            <el-table-column prop="passed" label="通过" width="60" />
            <el-table-column prop="failed" label="失败" width="60" />
            <el-table-column prop="rate" label="通过率" width="70">
              <template #default="scope">
                <span :class="getRateClass(scope.row.rate)">{{ scope.row.rate }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </template>

    <template #sidebar>
      <div class="filter-operations">
        <!-- 时间筛选 -->
        <div class="filter-group">
          <h4>时间筛选</h4>
          <el-form label-width="60px" size="small">
            <el-form-item label="时间段">
              <el-date-picker
                v-model="filters.timeRange"
                type="daterange"
                size="small"
                placeholder="选择时间范围"
              />
            </el-form-item>
            <el-form-item label="周期">
              <el-select v-model="filters.period" placeholder="选择周期">
                <el-option label="日" value="day" />
                <el-option label="周" value="week" />
                <el-option label="月" value="month" />
                <el-option label="季度" value="quarter" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- 状态筛选 -->
        <div class="filter-group">
          <h4>状态筛选</h4>
          <el-checkbox-group v-model="filters.status">
            <el-checkbox label="通过">通过</el-checkbox>
            <el-checkbox label="失败">失败</el-checkbox>
            <el-checkbox label="阻塞">阻塞</el-checkbox>
            <el-checkbox label="跳过">跳过</el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 分类筛选 -->
        <div class="filter-group">
          <h4>分类筛选</h4>
          <el-select v-model="filters.categories" multiple placeholder="选择分类">
            <el-option label="功能测试" value="function" />
            <el-option label="性能测试" value="performance" />
            <el-option label="安全测试" value="security" />
            <el-option label="兼容性测试" value="compatibility" />
          </el-select>
        </div>

        <!-- 应用筛选 -->
        <div class="filter-actions">
          <el-button type="primary" size="small" @click="applyFilters">应用筛选</el-button>
          <el-button size="small" @click="resetFilters">重置</el-button>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <h4>快速操作</h4>
          <div class="action-buttons">
            <el-button size="small" @click="viewDetails">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            <el-button size="small" @click="compareVersions">
              <el-icon><Rank /></el-icon>
              版本对比
            </el-button>
            <el-button size="small" @click="generateReport">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
          </div>
        </div>
      </div>
    </template>
  </FullScreenLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  ElButton,
  ElIcon,
  ElTable,
  ElTableColumn,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElCheckboxGroup,
  ElCheckbox,
  ElForm,
  ElFormItem,
} from 'element-plus'
import {
  Refresh,
  Download,
  Setting,
  PieChart,
  TrendCharts,
  Histogram,
  View,
  Rank,
  Document,
} from '@element-plus/icons-vue'
import FullScreenLayout from '@/components/ui/FullScreenLayout.vue'

// 统计数据
const statistics = reactive({
  totalUsecases: 1256,
  passedUsecases: 1089,
  failedUsecases: 167,
  passRate: 86.7,
})

// 分类数据
const categoryData = ref([
  { category: '功能测试', total: 456, passed: 398, failed: 58, rate: 87.3 },
  { category: '性能测试', total: 234, passed: 201, failed: 33, rate: 85.9 },
  { category: '安全测试', total: 123, passed: 109, failed: 14, rate: 88.6 },
  { category: '兼容性测试', total: 89, passed: 76, failed: 13, rate: 85.4 },
])

// 筛选条件
const filters = reactive({
  timeRange: [] as [Date, Date] | [],
  period: 'week',
  status: ['通过', '失败'],
  categories: [],
})

// 方法
const refreshData = () => {
  // 刷新数据逻辑
}

const exportReport = () => {
  // 导出报告逻辑
}

const openSettings = () => {
  // 打开设置逻辑
}

const getRateClass = (rate: number) => {
  if (rate >= 90) return 'rate-excellent'
  if (rate >= 80) return 'rate-good'
  if (rate >= 70) return 'rate-normal'
  return 'rate-poor'
}

const applyFilters = () => {
  // 应用筛选逻辑
}

const resetFilters = () => {
  filters.timeRange = []
  filters.period = 'week'
  filters.status = ['通过', '失败']
  filters.categories = []
}

const viewDetails = () => {
  // 查看详情逻辑
}

const compareVersions = () => {
  // 版本对比逻辑
}

const generateReport = () => {
  // 生成报告逻辑
}
</script>

<style scoped lang="scss">
.usecase-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: 100%;

  .chart-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e4e7ed;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .chart-placeholder {
      height: 200px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;

      .el-icon {
        font-size: 48px;
        margin-bottom: 8px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }

  .chart-section:nth-child(3) {
    grid-column: 1 / -1;
  }
}

.quality-analysis {
  .kpi-section {
    margin-bottom: 24px;

    .kpi-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .kpi-card {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        text-align: center;

        .kpi-value {
          font-size: 24px;
          font-weight: 700;
          color: #409eff;
          margin-bottom: 4px;
        }

        .kpi-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .trend-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .trend-chart {
      height: 120px;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;

      .el-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }

      p {
        margin: 0;
        font-size: 12px;
      }
    }
  }

  .category-stats {
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .rate-excellent {
      color: #67c23a;
      font-weight: 600;
    }

    .rate-good {
      color: #409eff;
      font-weight: 600;
    }

    .rate-normal {
      color: #e6a23c;
      font-weight: 600;
    }

    .rate-poor {
      color: #f56c6c;
      font-weight: 600;
    }
  }
}

.filter-operations {
  .filter-group {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }

  .filter-actions {
    margin-bottom: 24px;

    .el-button {
      width: 100%;
      margin-bottom: 8px;
    }
  }

  .quick-actions {
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .action-buttons {
      display: grid;
      gap: 8px;

      .el-button {
        justify-content: flex-start;
      }
    }
  }
}
</style>
