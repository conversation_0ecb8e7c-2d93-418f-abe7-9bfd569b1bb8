/**
 * 浏览器管理相关API类型定义
 * 标准化浏览器连接、状态管理等接口
 */

import type { ApiResponse } from './common'

/**
 * 浏览器状态数据接口
 */
export interface BrowserStatusData {
  status: string
  connected: boolean
  connection_state?: string
  remote_host?: string
  remote_port?: number
  pool_stats?: {
    active_connections: number
    total_connections: number
    total_users: number
  }
  user_pages_count?: number
  // 兼容不同API响应格式的属性
  is_connected?: boolean
  connection_detail?: string
}

/**
 * 浏览器状态响应接口
 */
export interface BrowserStatusResponse extends ApiResponse<BrowserStatusData> {
  status: 'success' | 'error'
  message: string
  // 直接属性支持，兼容不同响应格式
  is_connected?: boolean
  connection_detail?: string
  connected?: boolean
}

/**
 * 浏览器连接请求参数
 */
export interface BrowserConnectRequest {
  remote_host: string
  remote_port: number
}

/**
 * 浏览器连接响应数据
 */
export interface BrowserConnectData {
  user_id: string
  username: string
  remote_host: string
  remote_port: number
  connection_state: string
  user_pages_count: number
  pool_stats: {
    total_connections: number
    active_connections: number
    total_users: number
    total_pages: number
  }
}

/**
 * 浏览器连接响应接口
 */
export interface BrowserConnectResponse {
  status: string
  message: string
  data: BrowserConnectData
  timestamp: string
  request_id: string | null
}

/**
 * 浏览器断开连接数据接口
 */
export interface BrowserDisconnectData {
  user_id: string
  username: string
  connection_state: string
}

/**
 * 浏览器断开连接响应接口
 */
export interface BrowserDisconnectResponse {
  status: string
  message: string
  data: BrowserDisconnectData
  timestamp: string
  request_id: string | null
}

/**
 * 浏览器连接状态枚举
 */
export enum BrowserConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  TIMEOUT = 'timeout'
}

/**
 * 浏览器连接配置
 */
export interface BrowserConnectionConfig {
  autoReconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
  connectionTimeout?: number
}
