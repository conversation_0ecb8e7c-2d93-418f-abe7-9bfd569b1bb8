# 用例管理平台助手 - 浏览器扩展

这是一个强大的浏览器扩展，为用例管理平台提供便捷的悬浮窗访问方式，支持多用户隔离和跨域数据传输。

## 🎯 主要功能

### 1. 防干扰悬浮窗
- ✅ 在每个网页上创建独立的iframe悬浮窗
- ✅ 使用最高z-index确保永久可见
- ✅ 支持拖拽和调整大小
- ✅ 可控制透明度和点击穿透
- ✅ 多级监听确保不被页面移除

### 2. 多用户隔离系统
- ✅ 每个用户独立的API代理通道
- ✅ 分账号的IndexedDB数据存储
- ✅ 影子DOM隔离渲染环境
- ✅ 支持最多10个用户同时在线

### 3. 跨域数据传输
- ✅ postMessage + chrome.runtime 双重通信
- ✅ 安全的消息验证机制
- ✅ 支持实时数据同步
- ✅ 错误处理和重试机制

### 4. 智能管理
- ✅ 右键菜单快速操作
- ✅ 键盘快捷键 (Alt+U)
- ✅ 自动连接检测
- ✅ 设置导出/导入

## 🚀 安装方法

### 方法一：开发者模式安装（推荐）

1. **打开Chrome扩展管理页面**
   ```
   chrome://extensions/
   ```

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `frontend/browser-extension` 文件夹
   - 确认安装

### 方法二：打包安装

1. **打包扩展**
   ```bash
   # 在扩展目录中
   cd frontend/browser-extension
   # 压缩所有文件为zip包
   ```

2. **安装crx文件**
   - 将zip文件改名为.crx
   - 拖拽到Chrome扩展页面

## 📖 使用指南

### 首次使用

1. **安装完成后**
   - 扩展图标会出现在浏览器工具栏
   - 访问任意网页会自动显示悬浮窗
   - 首次需要刷新页面以激活

2. **配置设置**
   - 点击扩展图标打开设置面板
   - 调整透明度、启用状态等
   - 设置会自动保存并同步

### 基本操作

#### 悬浮窗操作
- **显示/隐藏**: `Alt + U` 或右键菜单
- **拖拽**: 拖拽标题栏移动位置
- **调整大小**: 拖拽右下角调整大小
- **最小化**: 点击标题栏的 `-` 按钮
- **最大化**: 点击标题栏的 `□` 按钮
- **关闭**: 点击标题栏的 `×` 按钮

#### 标签页切换
- **仪表板**: 快速概览和统计信息
- **用例**: 搜索和管理用例
- **API**: 测试API接口
- **设置**: 配置扩展参数

#### 快速操作
- **新建用例**: 直接跳转到平台创建页面
- **搜索用例**: 实时搜索功能
- **导出数据**: 导出当前数据为JSON
- **API测试**: 快速测试API接口

### 高级功能

#### 多用户支持
- 每个浏览器标签页可以独立登录不同用户
- 数据完全隔离，互不影响
- 支持同时管理多个账号

#### 数据同步
- 设置和偏好自动同步到云端
- 跨设备访问相同配置
- 数据备份和恢复

## ⚙️ 配置选项

### 悬浮窗设置
```json
{
  "enabled": true,          // 是否启用悬浮窗
  "position": {             // 默认位置
    "x": 50,
    "y": 50
  },
  "size": {                 // 默认大小
    "width": 400,
    "height": 300
  },
  "transparency": 0.9,      // 透明度 (0.3-1.0)
  "alwaysOnTop": true      // 始终置顶
}
```

### 连接设置
```json
{
  "platformUrl": "http://localhost:5174",      // 平台地址
        "apiUrl": "http://localhost:8000",     // API地址
  "autoConnect": true                          // 自动连接
}
```

## 🔧 开发说明

### 文件结构
```
browser-extension/
├── manifest.json          # 扩展配置文件
├── src/
│   └── background.js      # 后台服务脚本
├── content/
│   ├── content.js         # 内容脚本
│   ├── content.css        # 内容样式
│   ├── iframe.html        # 悬浮窗页面
│   ├── iframe.css         # 悬浮窗样式
│   └── iframe.js          # 悬浮窗脚本
├── popup/
│   ├── popup.html         # 弹窗页面
│   ├── popup.css          # 弹窗样式
│   └── popup.js           # 弹窗脚本
└── assets/               # 图标和资源
```

### 技术架构

#### 通信机制
```javascript
// 后台脚本 ↔ 内容脚本
chrome.runtime.sendMessage()
chrome.runtime.onMessage.addListener()

// 内容脚本 ↔ iframe
window.postMessage()
window.addEventListener('message')

// 跨域API请求
chrome.runtime.sendMessage({
  type: 'API_REQUEST',
  url: '/api/endpoint',
  method: 'POST',
  body: data
})
```

#### 数据存储
```javascript
// 同步设置存储
chrome.storage.sync.set(data)
chrome.storage.sync.get(keys)

// 本地数据存储
chrome.storage.local.set(data)
chrome.storage.local.get(keys)

// IndexedDB 分账号存储
const dbName = `usecase_platform_${userId}`
```

## 🐛 故障排除

### 常见问题

1. **悬浮窗不显示**
   - 检查扩展是否已启用
   - 刷新页面重新加载
   - 查看控制台错误信息

2. **API请求失败**
   - 确认后端服务已启动
   - 检查网络连接
   - 验证API地址配置

3. **设置不保存**
   - 检查Chrome同步是否启用
   - 清除扩展数据重新配置
   - 重新安装扩展

4. **性能问题**
   - 关闭不必要的标签页
   - 调整悬浮窗透明度
   - 减少API请求频率

### 调试方法

1. **查看后台脚本日志**
   ```
   chrome://extensions/ → 详细信息 → 检查视图：背景页
   ```

2. **查看内容脚本日志**
   ```
   F12 → Console → 查看扩展相关日志
   ```

3. **查看存储数据**
   ```javascript
   chrome.storage.sync.get(null, console.log)
   chrome.storage.local.get(null, console.log)
   ```

## 🔒 安全说明

### 权限使用
- `activeTab`: 访问当前标签页内容
- `storage`: 存储设置和数据
- `scripting`: 注入内容脚本
- `tabs`: 管理标签页
- `webNavigation`: 监听页面导航
- `contextMenus`: 创建右键菜单

### 数据保护
- 所有敏感数据都经过加密存储
- 用户数据完全隔离，不会泄露
- 不会收集任何个人隐私信息
- 符合GDPR等隐私保护规范

## 📞 支持与反馈

- **技术支持**: 发送邮件到 <EMAIL>
- **问题反馈**: 在GitHub仓库创建Issue
- **功能建议**: 通过扩展内的反馈功能提交

## 📝 更新日志

### v1.0.0 (2024-07-04)
- ✨ 初始版本发布
- ✨ 支持悬浮窗功能
- ✨ 多用户隔离系统
- ✨ 跨域数据传输
- ✨ 完整的设置管理

---

© 2024 用例管理平台团队 - 保留所有权利 