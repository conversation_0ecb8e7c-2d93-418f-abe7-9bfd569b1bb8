# AI配置页面提示词搜索功能说明

## 功能概述

AI配置页面现已集成完整的提示词搜索功能，支持实时搜索、中文关键词、以及友好的用户交互体验。

## 功能特性

### 🔍 搜索能力
- **实时搜索**: 输入关键词时自动触发搜索（300ms防抖）
- **中文支持**: 完全支持中文搜索关键词，如"测试"、"代码"等
- **多字段搜索**: 同时搜索提示词的标题和描述内容
- **URL编码**: 自动处理中文字符的URL编码

### 🎯 用户交互
- **搜索框**: 位于页面顶部，支持清空和加载状态显示
- **结果统计**: 实时显示搜索结果数量
- **空状态处理**: 搜索无结果时显示友好提示和清空按钮
- **快捷键**: Ctrl+F 快速聚焦到搜索框

### 📡 API集成
- **接口**: `GET /api/v1/prompts/prompts/`
- **参数**: `search`, `page`, `page_size`
- **认证**: JWT Bearer Token
- **编码**: 自动URL编码搜索关键词

## 使用方法

### 基本搜索
1. 在搜索框中输入关键词
2. 系统自动进行实时搜索（300ms防抖）
3. 查看搜索结果和统计信息

### 清空搜索
1. 点击搜索框的清空按钮，或
2. 在搜索结果为空时点击"清空搜索"按钮
3. 系统自动加载全部提示词

### 快捷键操作
- **Ctrl+F**: 快速聚焦到搜索框
- **Enter**: 手动触发搜索（通常不需要，因为有实时搜索）

## 技术实现

### 前端实现
```typescript
// 搜索函数
const loadPrompts = async (searchQuery?: string) => {
  const params: any = {
    page: 1,
    page_size: 100,
  }
  
  if (searchQuery && searchQuery.trim()) {
    params.search = searchQuery.trim()
  }
  
  const response = await apiService.prompts.getPrompts(params)
  // 处理响应...
}

// 防抖搜索
const debouncedSearch = useDebounceFn(async () => {
  await loadPrompts(searchKeyword.value)
}, 300)
```

### API调用
```bash
# 搜索示例
GET /api/v1/prompts/?search=%E6%B5%8B%E8%AF%95&page=1&page_size=20
Authorization: Bearer [JWT_TOKEN]
```

### URL编码处理
```typescript
// 自动处理中文编码
if (params?.search) {
  queryParams.append('search', encodeURIComponent(params.search))
}
```

## 搜索体验

### 搜索状态
- **输入中**: 显示loading状态
- **有结果**: 显示结果数量统计
- **无结果**: 显示友好的空状态提示
- **错误**: 显示具体的错误信息

### 响应式设计
- 搜索框自适应宽度
- 结果列表保持原有布局
- 移动端友好的交互体验

### 性能优化
- 300ms防抖避免频繁请求
- 智能缓存减少重复搜索
- 错误处理确保用户体验

## 搜索示例

### 中文搜索
- 搜索"测试" → 找到包含"测试"的提示词
- 搜索"代码" → 找到包含"代码"的提示词
- 搜索"AI" → 找到包含"AI"的提示词

### 英文搜索
- 搜索"test" → 找到包含"test"的提示词
- 搜索"code" → 找到包含"code"的提示词
- 搜索"prompt" → 找到包含"prompt"的提示词

### 组合搜索
- 搜索"测试代码" → 找到同时包含这些词的提示词
- 搜索"AI 助手" → 找到相关的AI助手提示词

## 错误处理

### 网络错误
- 显示具体的网络错误信息
- 提供重试建议
- 保持搜索状态不丢失

### 认证错误
- 自动处理token过期
- 引导用户重新登录
- 保存搜索关键词

### 服务器错误
- 显示服务器错误信息
- 提供故障排除建议
- 记录详细的错误日志

## 调试信息

### 控制台日志
```
🔍 执行搜索，关键词: 测试
🔍 搜索关键词: 测试 编码后: %E6%B5%8B%E8%AF%95
📡 提示词列表API响应: {...}
📈 分页信息: {total: 5, page: 1, search: "测试"}
```

### 网络请求
- 完整的请求URL和参数
- 响应状态和数据
- 错误详情和堆栈信息

## 未来扩展

### 高级搜索
- 按分类筛选
- 按状态筛选
- 按创建时间排序

### 搜索历史
- 保存搜索历史
- 快速搜索建议
- 热门搜索关键词

### 搜索分析
- 搜索统计
- 热门关键词
- 搜索效果分析
