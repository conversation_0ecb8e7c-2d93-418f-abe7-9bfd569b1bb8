/**
 * 统一的JWT双token认证管理器
 * 实现Access Token和Refresh Token的完整生命周期管理
 */

import { jwtDecode } from 'jwt-decode'
import { useUserStore } from '@/stores/user'

// Token配置
export const TOKEN_CONFIG = {
  ACCESS_TOKEN_LIFETIME: 30 * 60 * 1000, // 30分钟
  REFRESH_TOKEN_LIFETIME: 24 * 60 * 60 * 1000, // 1天
  REFRESH_THRESHOLD: 5 * 60 * 1000, // 提前5分钟刷新
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1秒
}

// Token存储键名
export const TOKEN_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  AUTH_TOKEN: 'auth_token', // 兼容旧版本
  USER_PROFILE: 'user_profile',
  TOKEN_EXPIRES: 'token_expires',
  TOKEN_ISSUED_AT: 'token_issued_at',
} as const

// JWT载荷接口
interface JWTPayload {
  exp: number
  iat: number
  sub: string
  username: string
  role: string
  [key: string]: any
}

// Token刷新结果接口
interface TokenRefreshResult {
  success: boolean
  accessToken?: string
  refreshToken?: string
  expiresAt?: number
  message?: string
}

// 全局状态
let isRefreshing = false
let refreshPromise: Promise<TokenRefreshResult> | null = null
let refreshAttempts = 0
let lastRefreshTime = 0

/**
 * 统一Token管理器类
 */
export class UnifiedTokenManager {
  private static instance: UnifiedTokenManager
  private refreshTimer: NodeJS.Timeout | null = null
  private eventListeners: Map<string, Function[]> = new Map()

  private constructor() {
    this.setupEventListeners()
  }

  static getInstance(): UnifiedTokenManager {
    if (!UnifiedTokenManager.instance) {
      UnifiedTokenManager.instance = new UnifiedTokenManager()
    }
    return UnifiedTokenManager.instance
  }

  /**
   * 获取当前用户的Access Token
   */
  getAccessToken(): string | null {
    try {
      // 1. 优先从多用户系统获取
      const userStore = useUserStore()
      if (userStore.token) {
        return userStore.token
      }
    } catch (_error) {
      // UserStore获取失败，继续尝试其他方式
    }

    // 2. 从localStorage获取
    let token = localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN) ||
                localStorage.getItem(TOKEN_KEYS.AUTH_TOKEN)
    if (token) return token

    // 3. 从sessionStorage获取
    token = sessionStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN) ||
            sessionStorage.getItem(TOKEN_KEYS.AUTH_TOKEN)
    if (token) return token

    return null
  }

  /**
   * 获取Refresh Token
   */
  getRefreshToken(): string | null {
    // 1. 从localStorage获取
    let token = localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN)
    if (token) return token

    // 2. 从sessionStorage获取
    token = sessionStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN)
    if (token) return token

    return null
  }

  /**
   * 设置Token
   */
  setTokens(accessToken: string, refreshToken?: string, rememberMe: boolean = false): void {
    const storage = rememberMe ? localStorage : sessionStorage

    // 存储Access Token
    storage.setItem(TOKEN_KEYS.ACCESS_TOKEN, accessToken)
    storage.setItem(TOKEN_KEYS.AUTH_TOKEN, accessToken) // 兼容性

    // 存储Refresh Token
    if (refreshToken) {
      storage.setItem(TOKEN_KEYS.REFRESH_TOKEN, refreshToken)
    }

    // 存储时间戳
    const now = Date.now()
    storage.setItem(TOKEN_KEYS.TOKEN_ISSUED_AT, now.toString())

    // 计算过期时间
    try {
      const payload = jwtDecode<JWTPayload>(accessToken)
      const expiresAt = payload.exp * 1000
      storage.setItem(TOKEN_KEYS.TOKEN_EXPIRES, expiresAt.toString())
    } catch (_error) {
      // JWT解析失败，使用默认过期时间
      const expiresAt = now + TOKEN_CONFIG.ACCESS_TOKEN_LIFETIME
      storage.setItem(TOKEN_KEYS.TOKEN_EXPIRES, expiresAt.toString())
    }

    // 清除另一个存储位置的token（避免冲突）
    const otherStorage = rememberMe ? sessionStorage : localStorage
    otherStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN)
    otherStorage.removeItem(TOKEN_KEYS.AUTH_TOKEN)
    otherStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN)

    // 触发token更新事件
    this.emit('tokenUpdated', { accessToken, refreshToken })
  }

  /**
   * 清除所有Token
   */
  clearTokens(): void {
    // 清除localStorage
    Object.values(TOKEN_KEYS).forEach(key => {
      localStorage.removeItem(key)
    })

    // 清除sessionStorage
    Object.values(TOKEN_KEYS).forEach(key => {
      sessionStorage.removeItem(key)
    })

    // 停止自动刷新
    this.stopAutoRefresh()

    // 触发token清除事件
    this.emit('tokenCleared')
  }

  /**
   * 检查Token是否过期
   */
  isTokenExpired(token?: string): boolean {
    const accessToken = token || this.getAccessToken()
    if (!accessToken) return true

    try {
      const payload = jwtDecode<JWTPayload>(accessToken)
      const now = Date.now() / 1000
      return payload.exp <= now
    } catch (_error) {
      return true
    }
  }

  /**
   * 检查Token是否即将过期
   */
  isTokenExpiringSoon(token?: string, thresholdMs: number = TOKEN_CONFIG.REFRESH_THRESHOLD): boolean {
    const accessToken = token || this.getAccessToken()
    if (!accessToken) return true

    try {
      const payload = jwtDecode<JWTPayload>(accessToken)
      const now = Date.now()
      const expiresAt = payload.exp * 1000
      return (expiresAt - now) <= thresholdMs
    } catch (_error) {
      return true
    }
  }

  /**
   * 智能Token刷新
   */
  async smartRefreshToken(): Promise<TokenRefreshResult> {
    const now = Date.now()

    // 防止频繁刷新
    if (now - lastRefreshTime < TOKEN_CONFIG.RETRY_DELAY) {
      return {
        success: false,
        message: '刷新过于频繁，请稍后再试'
      }
    }

    // 如果正在刷新，返回现有的Promise
    if (isRefreshing && refreshPromise) {
      return refreshPromise
    }

    const accessToken = this.getAccessToken()
    const refreshToken = this.getRefreshToken()

    // 检查是否需要刷新
    if (accessToken && !this.isTokenExpiringSoon(accessToken)) {
      return {
        success: true,
        accessToken,
        message: 'Token仍然有效'
      }
    }

    // 没有refresh token，需要重新登录
    if (!refreshToken) {
      return {
        success: false,
        message: '没有刷新令牌，需要重新登录'
      }
    }

    // 检查refresh token是否过期
    if (this.isTokenExpired(refreshToken)) {
      return {
        success: false,
        message: 'Refresh token已过期，需要重新登录'
      }
    }

    // 开始刷新
    isRefreshing = true
    lastRefreshTime = now

    refreshPromise = this.performTokenRefresh()

    try {
      const result = await refreshPromise
      return result
    } finally {
      isRefreshing = false
      refreshPromise = null
    }
  }

  /**
   * 执行Token刷新
   */
  private async performTokenRefresh(): Promise<TokenRefreshResult> {
    try {
      // 尝试使用多用户系统刷新
      const userStore = useUserStore()
      const result = await userStore.refreshToken()

      if (result.success && result.token) {
        // 更新token存储
        const rememberMe = !!localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN)
        this.setTokens(result.token, undefined, rememberMe)

        refreshAttempts = 0
        return {
          success: true,
          accessToken: result.token,
          message: 'Token刷新成功'
        }
      } else {
        throw new Error(result.message || 'Token刷新失败')
      }
    } catch (error: any) {
      refreshAttempts++

      if (refreshAttempts >= TOKEN_CONFIG.MAX_RETRY_ATTEMPTS) {
        // 达到最大重试次数，清除token并跳转登录
        this.clearTokens()
        this.emit('authenticationFailed', {
          message: 'Token刷新失败，请重新登录',
          error: error.message
        })

        return {
          success: false,
          message: 'Token刷新失败，已达到最大重试次数'
        }
      }

      return {
        success: false,
        message: error.message || 'Token刷新失败'
      }
    }
  }

  /**
   * 启动自动刷新监控
   */
  startAutoRefresh(): void {
    this.stopAutoRefresh()

    this.refreshTimer = setInterval(async () => {
      const accessToken = this.getAccessToken()

      if (!accessToken) {
        this.stopAutoRefresh()
        return
      }

      // 检查是否需要刷新
      if (this.isTokenExpiringSoon(accessToken)) {
        await this.smartRefreshToken()
      }
    }, 60000) // 每分钟检查一次
  }

  /**
   * 停止自动刷新监控
   */
  stopAutoRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  /**
   * 事件监听器设置
   */
  private setupEventListeners(): void {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // 页面重新可见时检查token状态
        const accessToken = this.getAccessToken()
        if (accessToken && this.isTokenExpiringSoon(accessToken, 10 * 60 * 1000)) {
          this.smartRefreshToken()
        }
      }
    })

    // 监听网络状态变化
    window.addEventListener('online', () => {
      // 网络恢复时检查token状态
      const accessToken = this.getAccessToken()
      if (accessToken && this.isTokenExpiringSoon(accessToken)) {
        this.smartRefreshToken()
      }
    })
  }

  /**
   * 事件发射器
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event) || []
    listeners.forEach(listener => {
      try {
        listener(data)
      } catch (error) {
        console.error(`事件监听器执行失败 [${event}]:`, error)
      }
    })
  }

  /**
   * 添加事件监听器
   */
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(listener)
  }

  /**
   * 移除事件监听器
   */
  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }
}

// 导出单例实例
export const tokenManager = UnifiedTokenManager.getInstance()
