import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'
import { usePermission } from '../composables/usePermission'
import { Permission } from '../types/api/user'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/app/dashboard',
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        requiresAuth: false,
        title: '登录',
      },
    },

    {
      path: '/app',
      name: 'App',
      component: () => import('../layouts/MainLayout.vue'),
      meta: {
        requiresAuth: true,
      },
      children: [
        // 仪表板
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('../views/DashboardView.vue'),
          meta: {
            title: '首页',
            // 所有用户都可以访问仪表板
          },
        },

        // 个人中心
        {
          path: 'profile',
          name: 'ProfileCenter',
          component: () => import('../views/ProfileCenter.vue'),
          meta: {
            title: '个人中心',
            // 所有登录用户都可以访问个人中心
          },
        },

        // 统计模块
        {
          path: 'statistics/outline',
          name: 'StatisticsOutline',
          component: () => import('../views/statistics/OutlineStatistics.vue'),
          meta: {
            title: '大纲统计',
            requiresPermission: Permission.VIEW_STATISTICS,
          },
        },
        {
          path: 'statistics/usecase',
          name: 'StatisticsUsecase',
          component: () => import('../views/statistics/UsecaseStatistics.vue'),
          meta: {
            title: '用例统计',
            requiresPermission: Permission.VIEW_STATISTICS,
          },
        },
        {
          path: 'statistics/dashboard',
          name: 'StatisticsDashboard',
          component: () => import('../views/statistics/StatisticsDashboard.vue'),
          meta: {
            title: '仪表盘',
            requiresPermission: Permission.VIEW_STATISTICS,
          },
        },

        // 追踪关系模块
        {
          path: 'tracking/relation',
          name: 'TrackingRelation',
          component: () => import('../views/tracking/TrackingRelation.vue'),
          meta: {
            title: '测试项目用例关系',
            requiresPermission: Permission.TRACKING_RELATION,
          },
        },

        // 管理库模块
        {
          path: 'management/outline',
          name: 'ManagementOutline',
          component: () => import('../views/management/OutlineLibrary.vue'),
          meta: {
            title: '大纲库',
            requiresPermission: Permission.MANAGE_OUTLINE,
          },
        },
        {
          path: 'management/usecase',
          name: 'ManagementUsecase',
          component: () => import('../views/management/UsecaseLibraryNew.vue'),
          meta: {
            title: '用例库管理',
            requiresPermission: Permission.MANAGE_USECASE,
          },
        },
        {
          path: 'management/report',
          name: 'ManagementReport',
          component: () => import('../views/management/ReportSummary.vue'),
          meta: {
            title: '问题报告单汇总',
            requiresPermission: Permission.MANAGE_REPORT,
          },
        },

        // 评审模块
        {
          path: 'review/outline',
          name: 'ReviewOutline',
          component: () => import('../views/review/OutlineReview.vue'),
          meta: {
            title: '大纲评审',
            requiresPermission: Permission.REVIEW_OUTLINE,
          },
        },
        {
          path: 'review/usecase',
          name: 'ReviewUsecase',
          component: () => import('../views/review/UsecaseReview.vue'),
          meta: {
            title: '用例评审',
            requiresPermission: Permission.REVIEW_USECASE,
          },
        },

        // AI模块
        {
          path: 'ai/config',
          name: 'AIConfig',
          component: () => import('../views/ai/AIConfig.vue'),
          meta: {
            title: 'AI相关配置',
            requiresPermission: Permission.AI_CONFIG,
          },
        },
        {
          path: 'ai/chat',
          name: 'AIChat',
          component: () => import('../views/ai/AIChat.vue'),
          meta: {
            title: 'AI对话',
            requiresPermission: Permission.AI_CHAT,
          },
        },

        // 报告编制模块
        {
          path: 'report/outline',
          name: 'ReportOutline',
          component: () => import('../views/report/OutlineReport.vue'),
          meta: {
            title: '大纲报告',
            requiresPermission: Permission.REPORT_OUTLINE,
          },
        },
        {
          path: 'report/usecase',
          name: 'ReportUsecase',
          component: () => import('../views/report/UsecaseReport.vue'),
          meta: {
            title: '用例报告',
            requiresPermission: Permission.REPORT_USECASE,
          },
        },
        {
          path: 'report/test',
          name: 'ReportTest',
          component: () => import('../views/report/TestReport.vue'),
          meta: {
            title: '测试报告',
            requiresPermission: Permission.REPORT_TEST,
          },
        },

        // 系统管理模块（仅管理员可访问）
        {
          path: 'admin/users',
          name: 'AdminUsers',
          component: () => import('../views/admin/UserManagement.vue'),
          meta: {
            title: '用户管理',
            requiresPermission: Permission.SYSTEM_ADMIN,
          },
        },
        {
          path: 'admin/system',
          name: 'AdminSystem',
          component: () => import('../views/admin/SystemSettings.vue'),
          meta: {
            title: '系统设置',
            requiresPermission: Permission.SYSTEM_ADMIN,
          },
        },

        // 权限管理模块（仅管理员可访问）
        {
          path: 'admin/permissions',
          name: 'AdminPermissions',
          component: () => import('../views/admin/PermissionManagement.vue'),
          meta: {
            title: '权限管理',
            requiresPermission: Permission.PERMISSION_MANAGEMENT,
          },
        },


      ],
    },
    {
      path: '/403',
      name: 'Forbidden',
      component: () => import('../views/ForbiddenView.vue'),
      meta: {
        title: '无权限访问',
      },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('../views/NotFoundView.vue'),
      meta: {
        title: '页面未找到',
      },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 检查认证状态：使用多用户系统的认证状态
  const loggedIn = userStore.isLoggedIn

  // 1. 处理需要认证的路由
  if (to.meta.requiresAuth && !loggedIn) {
    // 如果需要认证但未登录，重定向到登录页
    next({ name: 'Login', query: { redirect: to.fullPath } })
    return
  }

  // 2. 处理已登录用户访问登录页的情况
  if (to.name === 'Login' && loggedIn) {
    // 如果已登录，则重定向到主页
    next({ path: '/' })
    return
  }

  // 3. 权限检查
  if (to.meta.requiresPermission && loggedIn) {
    const { hasPermission } = usePermission()
    const requiredPermission = to.meta.requiresPermission as Permission

    if (!hasPermission(requiredPermission)) {
      // 没有权限，跳转到403页面
      next({ name: 'Forbidden' })
      return
    }
  }

  // 4. 更新页面标题
  if (to.meta.title && typeof to.meta.title === 'string') {
    document.title = `${to.meta.title} - 用例管理平台`
  }

  // 5. 其他情况，正常放行
  next()
})

export default router
