<template>
  <button
    class="tech-button"
    :class="[`variant-${variant}`]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <div v-if="loading" class="loader"></div>
    <span v-else class="content">
      <slot></slot>
    </span>
  </button>
</template>

<script setup lang="ts">
// 删除未使用的computed导入

const props = defineProps({
  variant: {
    type: String,
    default: 'primary', // 'primary', 'secondary'
  },
  loading: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['click'])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped lang="scss">
@use '@/assets/styles/theme.scss' as theme;

.tech-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: 1px solid theme.$color-primary;
  border-radius: theme.$border-radius-medium;
  background: linear-gradient(135deg, theme.$color-primary 0%, theme.$color-primary-dark 100%);
  color: theme.$color-text-light;
  font-family: theme.$font-family-primary;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  transition: all theme.$transition-fast;
  overflow: hidden;
  box-shadow: theme.$shadow-soft;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, theme.$color-primary-light 0%, theme.$color-primary 100%);
    transform: translateY(-2px);
    box-shadow: theme.$shadow-glow-primary;
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: theme.$shadow-soft;
  }

  &:disabled {
    background: theme.$color-secondary;
    border-color: theme.$color-secondary;
    color: theme.$color-text-disabled;
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loader {
  width: 18px;
  height: 18px;
  border: 2px solid theme.$color-text-disabled;
  border-top-color: theme.$color-text-light;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
