<template>
  <Teleport to="body">
    <Transition name="modal-fade">
      <div v-if="visible" class="hand-drawn-modal-overlay" @click="handleOverlayClick">
        <!-- 背景粒子效果 -->
        <div class="modal-particles">
          <div
            v-for="(particle, index) in backgroundParticles"
            :key="index"
            class="background-particle"
            :style="particle.style"
          />
        </div>

        <!-- 模态框主体 -->
        <div
          class="hand-drawn-modal"
          :class="[`modal-${size}`, { centered: centered }]"
          @click.stop
        >
          <!-- 手绘边框 -->
          <svg class="border-svg" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <linearGradient id="modalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" :style="{ stopColor: gradientColors[0] }" />
                <stop offset="50%" :style="{ stopColor: gradientColors[1] }" />
                <stop offset="100%" :style="{ stopColor: gradientColors[2] }" />
              </linearGradient>

              <filter id="modalGlow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
            </defs>

            <path
              :d="borderPath"
              fill="none"
              stroke="url(#modalGradient)"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              filter="url(#modalGlow)"
              :class="{ 'animated-border': animated }"
            />
          </svg>

          <!-- 模态框内容 -->
          <div class="modal-content">
            <!-- 模态框头部 -->
            <div class="modal-header">
              <slot name="header">
                <h2 v-if="title" class="modal-title">{{ title }}</h2>
                <p v-if="subtitle" class="modal-subtitle">{{ subtitle }}</p>
              </slot>

              <!-- 关闭按钮 -->
              <button v-if="showClose" class="modal-close" @click="handleClose" aria-label="关闭">
                <el-icon class="Close" />
              </button>
            </div>

            <!-- 模态框主体 -->
            <div class="modal-body">
              <slot />
            </div>

            <!-- 模态框底部 -->
            <div v-if="$slots.footer" class="modal-footer">
              <slot name="footer" />
            </div>
          </div>

          <!-- 发光效果 -->
          <div class="modal-glow"></div>

          <!-- 装饰性元素 -->
          <div class="modal-decorations">
            <div class="corner-decoration corner-tl"></div>
            <div class="corner-decoration corner-tr"></div>
            <div class="corner-decoration corner-bl"></div>
            <div class="corner-decoration corner-br"></div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  visible: boolean
  title?: string
  subtitle?: string
  size?: 'small' | 'medium' | 'large' | 'fullscreen'
  centered?: boolean
  showClose?: boolean
  closeOnOverlay?: boolean
  closeOnEscape?: boolean
  animated?: boolean
  gradientColors?: string[]
  borderType?: 'rounded' | 'angular' | 'organic' | 'tech'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  centered: true,
  showClose: true,
  closeOnOverlay: true,
  closeOnEscape: true,
  animated: true,
  gradientColors: () => ['#FFD700', '#4ECDC4', '#00D2FF'],
  borderType: 'rounded',
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 边框路径定义
const borderPaths = {
  rounded:
    'M10,20 Q20,10 30,20 Q40,30 50,20 Q60,10 70,20 Q80,30 90,20 L90,80 Q80,90 70,80 Q60,70 50,80 Q40,90 30,80 Q20,70 10,80 Z',
  angular: 'M5,15 L25,5 L45,15 L65,5 L85,15 L85,85 L65,95 L45,85 L25,95 L5,85 Z',
  organic:
    'M5,20 Q15,10 25,20 Q35,30 45,15 Q55,5 65,25 Q75,35 85,20 L85,80 Q75,90 65,75 Q55,85 45,70 Q35,80 25,85 Q15,75 5,80 Z',
  tech: 'M10,10 L30,10 L30,20 L40,20 L40,10 L60,10 L60,20 L70,20 L70,10 L90,10 L90,90 L70,90 L70,80 L60,80 L60,90 L40,90 L40,80 L30,80 L30,90 L10,90 Z',
}

// 计算属性
const borderPath = computed(() => borderPaths[props.borderType])

// 背景粒子效果
const backgroundParticles = ref([
  { style: { top: '10%', left: '10%', animationDelay: '0s' } },
  { style: { top: '20%', right: '15%', animationDelay: '1s' } },
  { style: { bottom: '30%', left: '20%', animationDelay: '2s' } },
  { style: { bottom: '20%', right: '10%', animationDelay: '3s' } },
  { style: { top: '50%', left: '5%', animationDelay: '4s' } },
  { style: { top: '60%', right: '5%', animationDelay: '5s' } },
])

// 事件处理
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose()
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.closeOnEscape) {
    handleClose()
  }
}

// 组件挂载和卸载
onMounted(() => {
  if (props.closeOnEscape) {
    document.addEventListener('keydown', handleKeydown)
  }

  // 禁止背景滚动
  document.body.style.overflow = 'hidden'
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = ''
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/theme.scss' as theme;

.hand-drawn-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;

  .modal-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .background-particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(255, 215, 0, 0.4);
      border-radius: 50%;
      animation: particleFloat 6s ease-in-out infinite;
    }
  }

  .hand-drawn-modal {
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    overflow: hidden;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;

    // 尺寸样式
    &.modal-small {
      width: 400px;
      min-height: 200px;
    }

    &.modal-medium {
      width: 600px;
      min-height: 300px;
    }

    &.modal-large {
      width: 800px;
      min-height: 400px;
    }

    &.modal-fullscreen {
      width: 95vw;
      height: 95vh;
    }

    // 边框SVG
    .border-svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;

      .animated-border {
        animation: borderPulse 4s ease-in-out infinite;
      }
    }

    // 模态框内容
    .modal-content {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 24px;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;

      .modal-title {
        margin: 0 0 8px 0;
        color: theme.$color-text-primary;
        font-size: 24px;
        font-weight: 600;
        text-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
      }

      .modal-subtitle {
        margin: 0;
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
      }

      .modal-close {
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.6);
        font-size: 24px;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: all theme.$transition-normal ease;
        margin-left: 16px;

        &:hover {
          color: rgba(255, 215, 0, 0.8);
          background: rgba(255, 215, 0, 0.1);
          transform: scale(1.1);
        }
      }
    }

    .modal-body {
      flex: 1;
      color: theme.$color-text-primary;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 215, 0, 0.3);
        border-radius: 3px;

        &:hover {
          background: rgba(255, 215, 0, 0.5);
        }
      }
    }

    .modal-footer {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }

    // 发光效果
    .modal-glow {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
      pointer-events: none;
      z-index: 1;
      border-radius: 20px;
    }

    // 装饰性元素
    .modal-decorations {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 1;

      .corner-decoration {
        position: absolute;
        width: 30px;
        height: 30px;
        border: 3px solid rgba(255, 215, 0, 0.4);
        opacity: 0.6;

        &.corner-tl {
          top: 15px;
          left: 15px;
          border-right: none;
          border-bottom: none;
        }

        &.corner-tr {
          top: 15px;
          right: 15px;
          border-left: none;
          border-bottom: none;
        }

        &.corner-bl {
          bottom: 15px;
          left: 15px;
          border-right: none;
          border-top: none;
        }

        &.corner-br {
          bottom: 15px;
          right: 15px;
          border-left: none;
          border-top: none;
        }
      }
    }
  }
}

// 过渡动画
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: all 0.3s ease;
}

.modal-fade-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.modal-fade-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

// 动画定义
@keyframes borderPulse {
  0%,
  100% {
    opacity: 0.6;
    stroke-width: 2;
  }
  50% {
    opacity: 1;
    stroke-width: 3;
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-20px) scale(1.3);
    opacity: 0.8;
  }
}

// 响应式设计
@media (max-width: theme.$breakpoint-mobile) {
  .hand-drawn-modal-overlay {
    padding: 10px;

    .hand-drawn-modal {
      &.modal-medium {
        width: 95vw;
        min-height: 60vh;
      }

      &.modal-large {
        width: 95vw;
        min-height: 80vh;
      }

      .modal-content {
        padding: 16px;
      }

      .modal-header .modal-title {
        font-size: 20px;
      }
    }
  }
}
</style>
