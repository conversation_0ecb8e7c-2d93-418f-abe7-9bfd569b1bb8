// 全局类型定义

// 环境变量类型扩展
// interface ImportMetaEnv {
//   readonly VITE_API_BASE_URL: string
//   readonly VITE_APP_TITLE: string
//   readonly VITE_APP_VERSION: string
// }

// interface ImportMeta {
//   readonly env: ImportMetaEnv
// }

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页数据类型
export interface PaginatedData<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 菜单项类型
export interface MenuItem {
  key: string
  label: string
  icon?: string
  route?: string
  children?: MenuItem[]
  disabled?: boolean
  hidden?: boolean
  permission?: string
}

// 删除未使用的表格和表单配置接口

// 导出类型
export * from '../adapters/usecaseAdapter'
