<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>录制控制台</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #fff;
      overflow: hidden;
      user-select: none;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
    }

    .recording-container {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 4px 6px;
      min-height: 27px;
      height: 35px;
      white-space: nowrap;
    }

    .usecase-name-input {
      flex-shrink: 0;
    }

    .usecase-name-input input {
      width: 120px;
      height: 22px;
      padding: 3px 6px;
      border: 1px solid #dcdfe6;
      border-radius: 3px;
      font-size: 11px;
      outline: none;
      transition: border-color 0.2s;
      line-height: 1.2;
    }

    .usecase-name-input input:focus {
      border-color: #409eff;
    }

    .usecase-name-input input::placeholder {
      color: #c0c4cc;
    }

    .control-buttons {
      display: flex;
      align-items: center;
      gap: 3px;
      flex-shrink: 0;
    }

    .btn {
      width: 24px;
      height: 24px;
      border: none;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 10px;
      color: white;
      transition: all 0.2s;
    }

    .btn:hover {
      transform: scale(1.05);
    }

    .btn-primary {
      background-color: #409eff;
    }

    .btn-primary:hover {
      background-color: #66b1ff;
    }

    .btn-warning {
      background-color: #e6a23c;
    }

    .btn-warning:hover {
      background-color: #ebb563;
    }

    .btn-success {
      background-color: #67c23a;
    }

    .btn-success:hover {
      background-color: #85ce61;
    }

    .btn-danger {
      background-color: #f56c6c;
    }

    .btn-danger:hover {
      background-color: #f78989;
    }

    .btn-info {
      background-color: #909399;
    }

    .btn-info:hover {
      background-color: #a6a9ad;
    }

    .close-button {
      flex-shrink: 0;
      margin-left: 3px;
    }

    .btn-close {
      width: 18px;
      height: 18px;
      border: none;
      background: transparent;
      color: #909399;
      cursor: pointer;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      transition: all 0.2s;
    }

    .btn-close:hover {
      background-color: #f5f7fa;
      color: #f56c6c;
    }

    .hidden {
      display: none !important;
    }

    /* 图标样式 */
    .icon {
      width: 12px;
      height: 12px;
      fill: currentColor;
    }
  </style>
</head>
<body>
  <div class="recording-container">
    <!-- 用例名称输入框 -->
    <div class="usecase-name-input">
      <input 
        type="text" 
        id="usecaseName" 
        placeholder="用例名称" 
        maxlength="50"
      />
    </div>

    <!-- 录制控制按钮 -->
    <div class="control-buttons">
      <button 
        id="startBtn" 
        class="btn btn-primary" 
        title="开始录制"
      >
        ▶
      </button>
      <button 
        id="pauseBtn" 
        class="btn btn-warning hidden" 
        title="暂停录制"
      >
        ⏸
      </button>
      <button 
        id="resumeBtn" 
        class="btn btn-success hidden" 
        title="继续录制"
      >
        ▶
      </button>
      <button 
        id="stopBtn" 
        class="btn btn-danger hidden" 
        title="停止录制"
      >
        ⏹
      </button>
      <button 
        id="screenshotBtn" 
        class="btn btn-info" 
        title="截图"
      >
        📷
      </button>
    </div>

    <!-- 关闭按钮 -->
    <div class="close-button">
      <button 
        id="closeBtn" 
        class="btn-close" 
        title="关闭"
      >
        ✕
      </button>
    </div>
  </div>

  <script>
    // 状态管理
    let isRecording = false;
    let isPaused = false;
    let usecaseName = '';

    // DOM 元素
    const elements = {
      usecaseNameInput: document.getElementById('usecaseName'),
      startBtn: document.getElementById('startBtn'),
      pauseBtn: document.getElementById('pauseBtn'),
      resumeBtn: document.getElementById('resumeBtn'),
      stopBtn: document.getElementById('stopBtn'),
      screenshotBtn: document.getElementById('screenshotBtn'),
      closeBtn: document.getElementById('closeBtn')
    };

    // 更新按钮显示状态
    function updateButtonStates() {
      elements.startBtn.classList.toggle('hidden', isRecording);
      elements.pauseBtn.classList.toggle('hidden', !isRecording || isPaused);
      elements.resumeBtn.classList.toggle('hidden', !isRecording || !isPaused);
      elements.stopBtn.classList.toggle('hidden', !isRecording);
    }

    // 向父窗口发送消息
    function sendMessageToParent(type, data = {}) {
      if (window.opener && !window.opener.closed) {
        window.opener.postMessage({
          type: 'recording-window-event',
          action: type,
          data: data
        }, '*');
      }
    }

    // 事件处理函数
    function handleStartRecording() {
      const name = elements.usecaseNameInput.value.trim();
      if (!name) {
        alert('请输入用例名称');
        elements.usecaseNameInput.focus();
        return;
      }

      isRecording = true;
      isPaused = false;
      usecaseName = name;
      updateButtonStates();
      
      sendMessageToParent('startRecord', { usecaseName: name });
    }

    function handlePauseRecording() {
      isPaused = true;
      updateButtonStates();
      sendMessageToParent('pauseRecord');
    }

    function handleResumeRecording() {
      isPaused = false;
      updateButtonStates();
      sendMessageToParent('resumeRecord');
    }

    function handleStopRecording() {
      isRecording = false;
      isPaused = false;
      updateButtonStates();
      sendMessageToParent('stopRecord');
    }

    function handleScreenshot() {
      sendMessageToParent('screenshot');
    }

    function handleClose() {
      sendMessageToParent('close');
      window.close();
    }

    // 绑定事件监听器
    elements.startBtn.addEventListener('click', handleStartRecording);
    elements.pauseBtn.addEventListener('click', handlePauseRecording);
    elements.resumeBtn.addEventListener('click', handleResumeRecording);
    elements.stopBtn.addEventListener('click', handleStopRecording);
    elements.screenshotBtn.addEventListener('click', handleScreenshot);
    elements.closeBtn.addEventListener('click', handleClose);

    // 回车键开始录制
    elements.usecaseNameInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter' && !isRecording) {
        handleStartRecording();
      }
    });

    // 监听来自父窗口的消息
    window.addEventListener('message', function(event) {
      if (event.data && event.data.type === 'recording-window-command') {
        const { action, data } = event.data;
        
        switch (action) {
          case 'updateState':
            isRecording = data.recording || false;
            isPaused = data.paused || false;
            if (data.usecaseName) {
              elements.usecaseNameInput.value = data.usecaseName;
            }
            updateButtonStates();
            break;
          case 'setUsecaseName':
            elements.usecaseNameInput.value = data.usecaseName || '';
            break;
        }
      }
    });

    // 窗口关闭时通知父窗口
    window.addEventListener('beforeunload', function() {
      sendMessageToParent('windowClosed');
    });

    // 初始化
    updateButtonStates();
    elements.usecaseNameInput.focus();

    // 通知父窗口窗口已准备就绪
    window.addEventListener('load', function() {
      sendMessageToParent('windowReady');
    });
  </script>
</body>
</html>
