/**
 * 报告管理相关API类型定义
 * 标准化报告生成、下载、管理等接口
 */

import type { BaseResponse } from './common'

/**
 * 报告生成请求接口
 */
export interface GenerateReportRequest {
  name: string
  type: string
  filters?: {
    systemName?: string
    dateRange?: {
      start: string
      end: string
    }
    status?: string[]
  }
}

/**
 * 报告生成响应数据
 */
export interface GenerateReportResponseData {
  export_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  message: string
  created_at: string
  estimated_completion?: string
}

/**
 * 报告生成响应接口
 */
export interface GenerateReportResponse extends BaseResponse<GenerateReportResponseData> {}

/**
 * 报告列表项接口
 */
export interface ReportListItem {
  export_id: string
  name: string
  type: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  created_at: string
  completed_at?: string
  file_size?: number
  download_count: number
  expires_at?: string
  error_message?: string
}

/**
 * 报告列表响应数据
 */
export interface ReportListResponseData {
  exports: ReportListItem[]
  total: number
  page: number
  page_size: number
}

/**
 * 报告列表响应接口
 */
export interface ReportListResponse extends BaseResponse<ReportListResponseData> {}

/**
 * 系统信息接口（用于报告生成）
 */
export interface ReportSystemInfo {
  name: string
  display_name: string
  description?: string
  case_count: number
  last_updated: string
}

/**
 * 系统列表响应数据
 */
export interface ReportSystemsResponseData {
  systems: ReportSystemInfo[]
  total: number
}

/**
 * 系统列表响应接口（用于报告）
 */
export interface ReportSystemsResponse extends BaseResponse<ReportSystemsResponseData> {}

/**
 * 报告下载响应（Blob类型）
 */
export type ReportDownloadResponse = Blob

/**
 * 报告状态枚举
 */
export enum ReportStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  EXPIRED = 'expired'
}

/**
 * 报告类型枚举
 */
export enum ReportType {
  TEST_CASES = '测试用例',
  EXECUTION_SUMMARY = '执行摘要',
  COVERAGE_REPORT = '覆盖率报告',
  DEFECT_REPORT = '缺陷报告'
}

/**
 * 报告格式枚举
 */
export enum ReportFormat {
  PDF = 'pdf',
  EXCEL = 'excel',
  CSV = 'csv',
  HTML = 'html'
}

/**
 * 报告配置接口
 */
export interface ReportConfig {
  format: ReportFormat
  includeCharts?: boolean
  includeDetails?: boolean
  template?: string
  customFields?: string[]
}

/**
 * 报告删除响应接口
 */
export interface ReportDeleteResponse extends BaseResponse<{
  export_id: string
  deleted_at: string
}> {}

/**
 * 报告统计信息接口
 */
export interface ReportStatistics {
  total_reports: number
  pending_reports: number
  completed_reports: number
  failed_reports: number
  total_downloads: number
  storage_used: number
  average_generation_time: number
}
