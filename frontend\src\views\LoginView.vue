<template>
  <div class="login-container">
    <div class="login-main">
      <div class="login-card">
        <div class="login-content">
          <!-- 登录标题 -->
          <div class="login-header">
            <h1 class="title">登录</h1>
          </div>

          <!-- 登录表单 -->
          <form class="login-form" @submit.prevent="handleLogin">
            <div class="form-group">
              <input
                v-model="loginForm.username"
                type="text"
                placeholder="用户名"
                class="form-input"
                required
              />
            </div>

            <div class="form-group">
              <input
                v-model="loginForm.password"
                type="password"
                placeholder="密码"
                class="form-input"
                required
              />
            </div>

            <div class="form-options">
              <div class="forgot-password">
                <a href="#" class="forgot-link">忘记密码？</a>
              </div>
            </div>

            <button
              type="submit"
              :disabled="loading || !loginForm.username || !loginForm.password"
              class="login-button"
            >
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </form>

          <!-- 错误信息 -->
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useMultiUserSystem } from '@/composables/useMultiUserSystem'
import type { LoginForm } from '@/types/api/user'

// 路由
const router = useRouter()

// 多用户系统
const multiUserSystem = useMultiUserSystem()

// 响应式数据
const loading = ref(false)
const errorMessage = ref('')

// 登录表单数据
const loginForm = reactive<LoginForm>({
  username: '',
  password: '',
})

// 处理登录
const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    errorMessage.value = '请填写用户名和密码'
    return
  }

  try {
    loading.value = true
    errorMessage.value = ''

    // 使用多用户系统登录
    const result = await multiUserSystem.login(loginForm.username, loginForm.password)

    if (result.success) {
      ElMessage.success({
        message: '登录成功',
        type: 'success',
      })

      // 跳转到首页
      router.push('/')
    } else {
      errorMessage.value = result.message || '登录失败'
    }
  } catch (error) {
    errorMessage.value = '登录时发生错误，请重试'
  } finally {
    loading.value = false
  }
}

// 组件挂载
onMounted(() => {
  // 恢复记住的用户名
  const rememberedUsername = localStorage.getItem('username')
  const isRemembered = localStorage.getItem('remember_me') === 'true'

  if (isRemembered && rememberedUsername) {
    loginForm.username = rememberedUsername
  }
})
</script>

<style scoped lang="scss">
.login-container {
  position: fixed;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f0f2f0 0%, #e8ebe8 100%);
  min-height: 100vh;

  .login-main {
    width: 100%;
    max-width: 400px;
    padding: 20px;

    .login-card {
      width: 100%;
      padding: 48px 40px;
      background: #fafbfa;
      border-radius: 24px;
      box-shadow: 0 32px 64px rgba(105, 123, 107, 0.15);
      backdrop-filter: blur(10px);

      .login-content {
        .login-header {
          text-align: center;
          margin-bottom: 40px;

          .title {
            font-size: 32px;
            font-weight: 600;
            color: #5a6b5d;
            margin: 0;
            letter-spacing: -0.5px;
          }
        }

        .login-form {
          .form-group {
            margin-bottom: 24px;

            .form-input {
              width: 100%;
              height: 56px;
              padding: 16px 20px;
              border: 1px solid #e0e4e0;
              border-radius: 12px;
              font-size: 16px;
              color: #5a6b5d;
              background: #f5f7f5;
              transition: all 0.3s ease;
              box-sizing: border-box;

              &::placeholder {
                color: #9ba69d;
              }

              &:focus {
                outline: none;
                border-color: #8db4a0;
                background: #fafbfa;
                box-shadow: 0 0 0 3px rgba(141, 180, 160, 0.15);
              }
            }
          }

          .form-options {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 32px;

            .forgot-password {
              .forgot-link {
                font-size: 14px;
                color: #8a9688;
                text-decoration: none;
                transition: color 0.3s ease;

                &:hover {
                  color: #5a6b5d;
                }
              }
            }
          }

          .login-button {
            width: 100%;
            height: 56px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            background: linear-gradient(135deg, #b8c5a6 0%, #a8b896 100%);
            border: none;
            color: #414e44;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(184, 197, 166, 0.4);

            &:hover:not(:disabled) {
              transform: translateY(-2px);
              box-shadow: 0 8px 20px rgba(184, 197, 166, 0.5);
              background: linear-gradient(135deg, #c2cea9 0%, #b2c19f 100%);
            }

            &:active:not(:disabled) {
              transform: translateY(0);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
              transform: none;
            }
          }
        }

        .error-message {
          text-align: center;
          color: #b58a8a;
          font-size: 14px;
          margin-top: 20px;
          padding: 12px 16px;
          background: rgba(181, 138, 138, 0.1);
          border-radius: 8px;
          border: 1px solid rgba(181, 138, 138, 0.2);
        }
      }
    }
  }
}
</style>
