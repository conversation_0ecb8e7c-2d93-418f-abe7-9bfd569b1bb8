/**
 * 录制管理相关API类型定义
 * 标准化录制操作、状态管理等接口
 */

import type { BaseResponse } from './common'

/**
 * 录制响应数据接口
 */
export interface RecordingResponseData {
  user_id: string
  file_path: string | null
}

/**
 * 录制基础响应接口
 */
export interface RecordingBaseResponse {
  status: string
  message: string
  success?: boolean
  data: RecordingResponseData
  timestamp: string
  request_id: string | null
}

/**
 * 开始录制响应接口
 */
export interface StartRecordingResponse extends RecordingBaseResponse {}

/**
 * 暂停录制响应接口
 */
export interface PauseRecordingResponse extends RecordingBaseResponse {}

/**
 * 继续录制响应接口
 */
export interface ResumeRecordingResponse extends RecordingBaseResponse {}

/**
 * 停止录制响应接口
 */
export interface StopRecordingResponse extends RecordingBaseResponse {}

/**
 * 取消录制响应接口
 */
export interface CancelRecordingResponse extends RecordingBaseResponse {}

/**
 * 录制状态枚举
 */
export enum RecordingStatus {
  IDLE = 'idle',
  RECORDING = 'recording',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  ERROR = 'error'
}

/**
 * 录制配置接口
 */
export interface RecordingConfig {
  autoSave?: boolean
  saveInterval?: number
  maxDuration?: number
  quality?: 'low' | 'medium' | 'high'
  includeAudio?: boolean
}

/**
 * 录制文件信息接口
 */
export interface RecordingFileInfo {
  file_path: string
  file_size: number
  duration: number
  created_at: string
  format: string
}

/**
 * 录制操作响应接口（兼容格式）
 */
export interface RecordingResponse {
  success: boolean
  message: string
  user_id: string
  file_path?: string | null
}

/**
 * 录制状态查询响应
 */
export interface RecordingStatusResponse extends BaseResponse<{
  status: RecordingStatus
  user_id: string
  start_time?: string
  duration?: number
  file_path?: string
}> {}

/**
 * 录制历史记录接口
 */
export interface RecordingHistory {
  id: string
  user_id: string
  file_path: string
  file_name: string
  duration: number
  created_at: string
  file_size: number
  status: 'completed' | 'failed' | 'processing'
}

/**
 * 录制历史列表响应
 */
export interface RecordingHistoryResponse extends BaseResponse<{
  recordings: RecordingHistory[]
  total: number
  page: number
  page_size: number
}> {}
